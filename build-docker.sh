#!/bin/bash
# Smart Docker Build Script - Chỉ build service có thay đổi
# Sử dụng: ./smart-build-docker.sh [service1] [service2] ...
# Options: --no-cache, --pull, --force-rebuild, --check-only

set -e # Exit on any error

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Build options
USE_CACHE=true
PULL_IMAGES=false
FORCE_REBUILD=false
PARALLEL_BUILD=true
CHECK_ONLY=false
BUILD_ARGS=""

# Cache file để lưu trạng thái trước đó
CACHE_FILE=".build-cache.json"
LAST_COMMIT_FILE=".last-commit-hash"

# Function để log với màu
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_cache() {
    echo -e "${PURPLE}[CACHE]${NC} $1"
}

log_detect() {
    echo -e "${CYAN}[DETECT]${NC} $1"
}

# Service configuration - mapping service với thư mục và dependencies
declare -A SERVICE_PATHS=(
    ["domain-manager"]="domain-manager"
    ["smm-admin"]="smm-admin"
    ["smm-dashboard"]="smm-dashboard"
    ["smm-system"]="smm-system"
)

# Dependencies giữa các services
declare -A SERVICE_DEPS=(
    ["smm-admin"]="smm-system"
    ["smm-dashboard"]="smm-system"
    ["domain-manager"]="smm-system"
)

# Shared paths - nếu thay đổi sẽ build lại tất cả
SHARED_PATHS=(
    "docker-compose.yml"
    "docker-compose.override.yml"
    ".env"
    ".env.example"
    "Dockerfile"
    "package.json"
    "package-lock.json"
    "composer.json"
    "composer.lock"
    "requirements.txt"
    "go.mod"
    "go.sum"
)

# Function để show help
show_help() {
    echo "Smart Docker Build Script - Build only changed services"
    echo "Usage: $0 [OPTIONS] [SERVICE...]"
    echo ""
    echo "Options:"
    echo "  --no-cache        Disable Docker layer cache"
    echo "  --pull            Pull latest base images before building"
    echo "  --force-rebuild   Force rebuild without checking changes"
    echo "  --all             Build all services (same as --force-rebuild)"
    echo "  --no-parallel     Disable parallel building"
    echo "  --check-only      Only check for changes, don't build"
    echo "  --help           Show this help message"
    echo ""
    echo "Services:"
    for service in "${!SERVICE_PATHS[@]}"; do
        echo "  $service"
    done
    echo ""
    echo "Examples:"
    echo "  $0                           # Build only changed services"
    echo "  $0 --check-only              # Check what needs to be built"
    echo "  $0 --all                     # Build all services"
    echo "  $0 --force-rebuild           # Force rebuild all services"
    echo "  $0 smm-admin                 # Build specific service if changed"
}

# Function để parse arguments
parse_arguments() {
    SERVICES_TO_BUILD=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --no-cache)
                USE_CACHE=false
                BUILD_ARGS="$BUILD_ARGS --no-cache"
                shift
                ;;
            --pull)
                PULL_IMAGES=true
                BUILD_ARGS="$BUILD_ARGS --pull"
                shift
                ;;
            --force-rebuild)
                FORCE_REBUILD=true
                shift
                ;;
            --all)
                FORCE_REBUILD=true
                shift
                ;;
            --no-parallel)
                PARALLEL_BUILD=false
                shift
                ;;
            --check-only)
                CHECK_ONLY=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            -*)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -n "${SERVICE_PATHS[$1]}" ]]; then
                    SERVICES_TO_BUILD="$SERVICES_TO_BUILD $1"
                else
                    log_error "Unknown service: $1"
                    log_info "Available services: ${!SERVICE_PATHS[*]}"
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# Function để kiểm tra Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
}

# Function để kiểm tra thư mục service có tồn tại
check_service_directory() {
    local service=$1
    local service_path="${SERVICE_PATHS[$service]}"
    
    if [ ! -d "$service_path" ]; then
        log_error "Service directory '$service_path' does not exist"
        return 1
    fi
    
    return 0
}

# Function để get current commit hash
get_current_commit() {
    git rev-parse HEAD 2>/dev/null || echo "unknown"
}

# Function để get last commit hash
get_last_commit() {
    if [ -f "$LAST_COMMIT_FILE" ]; then
        cat "$LAST_COMMIT_FILE"
    else
        echo "none"
    fi
}

# Function để save current commit hash
save_current_commit() {
    get_current_commit > "$LAST_COMMIT_FILE"
}

# Function để get changed files giữa 2 commits
get_changed_files() {
    local last_commit=$(get_last_commit)
    local current_commit=$(get_current_commit)
    
    if [ "$last_commit" = "none" ] || [ "$FORCE_REBUILD" = true ]; then
        # First run hoặc force rebuild - return all files
        find . -type f -name "*.php" -o -name "*.js" -o -name "*.ts" -o -name "*.go" -o -name "*.py" -o -name "Dockerfile*" -o -name "docker-compose*" -o -name "*.json" -o -name "*.yml" -o -name "*.yaml" | head -100
        return
    fi
    
    if [ "$last_commit" = "$current_commit" ]; then
        # No changes
        return
    fi
    
    # Get changed files between commits
    git diff --name-only "$last_commit" "$current_commit" 2>/dev/null || true
}

# Function để check if shared files changed
check_shared_files_changed() {
    local changed_files="$1"
    
    for shared_path in "${SHARED_PATHS[@]}"; do
        if echo "$changed_files" | grep -q "^$shared_path"; then
            log_detect "Shared file changed: $shared_path"
            return 0
        fi
    done
    
    return 1
}

# Function để detect services cần build
detect_services_to_build() {
    local changed_files=$(get_changed_files)
    local services_to_build=""
    
    if [ -z "$changed_files" ]; then
        log_detect "No changes detected since last build"
        return
    fi
    
    log_detect "Changed files:"
    echo "$changed_files" | sed 's/^/  /'
    
    # Check shared files
    if check_shared_files_changed "$changed_files"; then
        log_detect "Shared files changed - all services need rebuild"
        services_to_build="${!SERVICE_PATHS[*]}"
        echo "$services_to_build"
        return
    fi
    
    # Check individual services
    for service in "${!SERVICE_PATHS[@]}"; do
        local service_path="${SERVICE_PATHS[$service]}"
        
        # Check if service files changed
        if echo "$changed_files" | grep -q "^$service_path/"; then
            log_detect "Service $service changed (files in $service_path/)"
            if [[ ! " $services_to_build " =~ " $service " ]]; then
                services_to_build="$services_to_build $service"
            fi
        fi
        
        # Check dependencies
        if [[ -n "${SERVICE_DEPS[$service]}" ]]; then
            local dep_service="${SERVICE_DEPS[$service]}"
            local dep_path="${SERVICE_PATHS[$dep_service]}"
            
            if echo "$changed_files" | grep -q "^$dep_path/"; then
                log_detect "Service $service needs rebuild (dependency $dep_service changed)"
                if [[ ! " $services_to_build " =~ " $service " ]]; then
                    services_to_build="$services_to_build $service"
                fi
            fi
        fi
    done
    
    # Clean up extra spaces
    services_to_build=$(echo "$services_to_build" | xargs)
    echo "$services_to_build"
}

# Function để build một service
build_service() {
    local service=$1
    local service_path="${SERVICE_PATHS[$service]}"
    
    # Check if service directory exists
    if ! check_service_directory "$service"; then
        return 1
    fi
    
    # Check if Dockerfile exists
    if [ ! -f "$service_path/Dockerfile" ]; then
        log_error "Dockerfile not found in '$service_path/'"
        return 1
    fi
    
    log_info "Building $service from directory: $service_path"
    
    # Build Docker image
    local build_cmd="docker build"
    
    if [ "$USE_CACHE" = false ]; then
        build_cmd="$build_cmd --no-cache"
    fi
    
    if [ "$PULL_IMAGES" = true ]; then
        build_cmd="$build_cmd --pull"
    fi
    
    # Tag the image with service name
    build_cmd="$build_cmd -t $service:latest $service_path"
    
    log_info "Executing: $build_cmd"
    
    if eval "$build_cmd"; then
        log_success "Successfully built $service"
        return 0
    else
        log_error "Failed to build $service"
        return 1
    fi
}

# Function để build services song song
build_services_parallel() {
    local services_str="$1"
    local services_array=($services_str)
    local pids=()
    
    log_info "Building ${#services_array[@]} services in parallel..."
    
    for service in "${services_array[@]}"; do
        if [ -n "$service" ]; then
            build_service "$service" &
            pids+=($!)
        fi
    done
    
    local failed_services=""
    for i in "${!pids[@]}"; do
        if ! wait "${pids[$i]}"; then
            failed_services="$failed_services ${services_array[$i]}"
        fi
    done
    
    if [ -n "$failed_services" ]; then
        log_error "Failed to build services:$failed_services"
        return 1
    fi
    
    return 0
}

# Function để build services tuần tự
build_services_sequential() {
    local services=$1
    
    for service in $services; do
        if ! build_service "$service"; then
            log_error "Failed to build $service"
            return 1
        fi
    done
    
    return 0
}

# Function để show built images
show_built_images() {
    local services=$1
    
    if [ -z "$services" ]; then
        return 0
    fi
    
    log_info "Built Docker images:"
    for service in $services; do
        local image_info=$(docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep "^$service:")
        if [ -n "$image_info" ]; then
            echo "  $image_info"
        fi
    done
}

# Function để cleanup old images
cleanup_old_images() {
    log_info "Cleaning up dangling images..."
    docker image prune -f &> /dev/null || true
    log_cache "Cleanup completed"
}

# Function để show build statistics
show_build_stats() {
    local built_services=$1
    local start_time=$2
    local end_time=$3
    
    local duration=$((end_time - start_time))
    
    echo ""
    log_info "=== BUILD STATISTICS ==="
    echo "Build duration: ${duration}s"
    
    if [ -n "$built_services" ]; then
        local count=$(echo $built_services | wc -w)
        echo "Built services ($count): $built_services"
    else
        echo "No services built"
    fi
    
    if [ "$USE_CACHE" = true ]; then
        log_cache "Docker layer cache was enabled"
    fi
}

# Main logic
main() {
    local start_time=$(date +%s)
    
    log_info "Smart Docker Build - Build only changed services"
    
    # Parse command line arguments
    parse_arguments "$@"
    
    # Check prerequisites
    check_docker
    
    # Pull latest code
    log_info "Pulling latest code..."
    if git pull; then
        log_success "Code updated successfully"
    else
        log_warning "Could not update code (continuing with local changes)"
    fi
    
    # Detect services to build
    local detected_services
    if [ -n "$SERVICES_TO_BUILD" ]; then
        detected_services="$SERVICES_TO_BUILD"
        log_info "Building specified services: $detected_services"
    else
        detected_services=$(detect_services_to_build)
        if [ -z "$detected_services" ]; then
            log_success "No services need rebuilding - all up to date!"
            save_current_commit
            exit 0
        fi
        log_info "Auto-detected services to build: $detected_services"
    fi
    
    # Check only mode
    if [ "$CHECK_ONLY" = true ]; then
        log_info "Check-only mode - services that would be built:"
        echo "$detected_services" | tr ' ' '\n' | sed 's/^/  /'
        exit 0
    fi
    
    # Setup build environment
    if [ "$USE_CACHE" = true ]; then
        log_cache "Using Docker layer cache"
        export DOCKER_BUILDKIT=1
    fi
    
    # Build services
    local build_success=true
    if [ "$PARALLEL_BUILD" = true ] && [ $(echo $detected_services | wc -w) -gt 1 ]; then
        if ! build_services_parallel "$detected_services"; then
            build_success=false
        fi
    else
        if ! build_services_sequential "$detected_services"; then
            build_success=false
        fi
    fi
    
    if [ "$build_success" = false ]; then
        log_error "Build failed for some services"
        exit 1
    fi
    
    # Show built images
    show_built_images "$detected_services"
    
    # Cleanup
    cleanup_old_images
    
    # Save current commit hash
    save_current_commit
    
    # Show statistics
    show_build_stats "$detected_services" $start_time $(date +%s)
    
    log_success "Smart build completed successfully!"
    log_info "Next run will only build services with new changes"
}

# Chạy main function với tất cả arguments
main "$@"