server:
  port: 8095
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
      force-request: true
      force-response: true



# Application Configuration
app:
  subscription:
    # Renewal cost in USD or your currency
    renewal-cost: 7.7
    # Default renewal period in days
    default-renewal-days: 30
    # Grace period for suspended tenants in days
    grace-period-days: 7
    # Days ahead to check for expiring tenants
    expiration-notification-days: 7




logging:
  file:
    name: /var/log/smm-system.log
  logback:
    rolling policy:
      max-file-size: 10MB
      max-history: 7
  level:
    root: info
    tndung.vnfb.smm.config.TenantAuthenticationFilter: debug
    tndung.vnfb.smm.config.AuthenticationFilter: debug
    tndung.vnfb.smm.config.TenantContext: debug
  pattern:
    console: '%d{HH:mm:ss} [%thread] %highlight(%-5level) %logger{30} - %msg%n'
    file: '%d{MM-dd HH:mm:ss} [%thread] %-5level %logger{30} - %msg%n'


spring:
#  http:
#    proxy:
#      host: **************
#      port: 6540
#      username: elmvabnu
#      password: pdsf2udoqsbb
  jackson:
    property-naming-strategy: SNAKE_CASE
  profiles:
    active: @spring.profiles.active@
  messages:
    encoding: UTF-8
    basename: messages
    cache-duration: 3600
  task:
    execution:
      pool:
        core-size: 3
        max-size: 10
        queue-capacity: 200
        keep-alive: 60s
      thread-name-prefix: "spring-async-"

    # Transaction event configuration
    transaction:
      rollback-on-commit-failure: true



#  autoconfigure:
#    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

config:
  thread:
    request:
      core-pool-size: 2
      max-pool-size: 4
      thread-name-prefix: vnfb-async
    batch:
      core-pool-size: 10
      max-pool-size: 30
      thread-name-prefix: bot-async

jwt:
  header:
    authorization: authorization
    client-id: x-client-id
    refresh-token: x-refresh-token
    prefix: Bearer
  access-token:
    validity: 172800
  refresh-token:
    validity: 604800
  auth:
    token-name: token
  private-key: nio-8096-exec-1
  public-key: nio-8096-exec-8
  authorities:
    key: roles


currency:
  api.url: https://api.exchangerate-api.com/v4/latest/
  api.base-currency: USD

# File upload configuration
file:
  upload:
    dir: /app/uploads
    max-size: 10MB
  cdn:
    url: http://localhost/cdn

# Logo generator configuration
logo:
  generator:
    script:
      path: scripts/logo-generator.js
    timeout: 30000
    temp:
      dir: /app/temp/logos

# Telegram proxy configuration
telegram:
  proxy:
    servers:
      - host: **************
        port: 6540
        username: elmvabnu
        password: pdsf2udoqsbb
      - host: ***************
        port: 6712
        username: elmvabnu
        password: pdsf2udoqsbb
      - host: **************
        port: 6543
        username: elmvabnu
        password: pdsf2udoqsbb
      - host: ************
        port: 6349
        username: elmvabnu
        password: pdsf2udoqsbb
      - host: *************
        port: 6837
        username: elmvabnu
        password: pdsf2udoqsbb
      - host: ************
        port: 6661
        username: elmvabnu
        password: pdsf2udoqsbb
      - host: *************
        port: 6732
        username: elmvabnu
        password: pdsf2udoqsbb
      - host: **************
        port: 6593
        username: elmvabnu
        password: pdsf2udoqsbb
      - host: ***************
        port: 6655
        username: elmvabnu
        password: pdsf2udoqsbb
      - host: **********
        port: 5653
        username: elmvabnu
        password: pdsf2udoqsbb
  # Notification bot configuration
  notification:
    bot:
      token: "7417775985:AAFoxyuRUyUjBh0HP9cXTytiCQmIHyp9AGE"
      username: "kiotvietphatbot"
#http:
#  proxyHost: **************
#  proxyPort: 6540
#  proxyUser: elmvabnu
#  proxyPassword: pdsf2udoqsbb
#
#https:
#  proxyHost: **************
#  proxyPort: 6540
#  proxyUser: elmvabnu
#  proxyPassword: pdsf2udoqsbb

# Dripfeed configuration
dripfeed:
  processing:
    # Interval for checking ready dripfeeds (in milliseconds)
    ready-check-interval: 30000  #30 seconds
    # Interval for checking running loop dripfeeds (in milliseconds)
    loop-check-interval: 60000   # 1 minute
    # Maximum number of dripfeeds to process in one batch
    batch-size: 50

# GitLab Error Tracking configuration
gitlab:
  error-tracking:
    # Enable/disable GitLab error tracking
    enabled: true
    filter-library-frames: true
    include-source-code: true
    # GitLab project ID (numeric ID of your project)
    project-id: "69872852"
    # GitLab instance URL
    gitlab-url: "https://gitlab.com"
    # GitLab access token with API scope
    access-token: "**************************"
    # Sentry DSN generated by GitLab (get this from GitLab project settings)
    sentry-dsn: "https://<EMAIL>:443/errortracking/api/v1/projects/69872852"
    # Environment name
    environment: "master"
    # Release version (can be set dynamically)
    release: ""
    # Server name
    server-name: "smm-system"
    # Sample rate (0.0 to 1.0)
    sample-rate: 1.0
    # Debug mode
    debug: false
    # Custom tags
    tags:
      service: "smm-system"
      component: "backend"
    # Capture settings
    capture-unhandled-exceptions: true
    capture-handled-exceptions: true
    # Minimum log level to capture
    minimum-log-level: "ERROR"


