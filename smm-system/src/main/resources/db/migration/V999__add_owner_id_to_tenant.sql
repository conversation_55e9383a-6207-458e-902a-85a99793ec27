-- Add owner_id column to tenant table
ALTER TABLE tenant ADD COLUMN IF NOT EXISTS owner_id BIGINT;

-- Add foreign key constraint to g_user table
ALTER TABLE tenant ADD CONSTRAINT fk_tenant_owner 
    FOREIG<PERSON> KEY (owner_id) REFERENCES g_user(id);

-- Add index for better query performance
CREATE INDEX IF NOT EXISTS idx_tenant_owner_id ON tenant(owner_id);

-- Add comment for documentation
COMMENT ON COLUMN tenant.owner_id IS 'ID of the user who owns this tenant';
