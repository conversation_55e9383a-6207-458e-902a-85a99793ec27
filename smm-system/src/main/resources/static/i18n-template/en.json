{"datetime_picker": {"select_time": "Select time", "selected_date": "Selected date", "cancel": "Cancel", "apply": "Apply"}, "common": {"loading": "Loading", "error": "Error", "redirecting": "Redirecting", "created": "Created", "updated": "Updated", "filter": "Filter", "search": "Search", "date_range": "Date Range", "close": "Close", "category": "Category", "service": "Service", "platform": "Platform", "reset": "Reset", "apply": "Apply", "actions": "Actions", "sending": "Sending...", "go_to_dashboard": "Go to Dashboard", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "view": "View", "no_data": "No data", "select_all": "Select all", "status": "Status", "user": "User", "link": "Link", "quantity": "Quantity", "price": "Price", "total_amount": "Total amount", "description": "Description", "note": "Note", "example": "Example", "selected": "selected", "information": "Information", "email": "Email", "password": "Password", "username": "Username", "full_name": "Full name", "phone_number": "Phone number", "balance": "Balance", "amount": "Amount", "date": "Date", "time": "Time", "id": "ID", "name": "Name", "type": "Type", "content": "Content", "subject": "Subject", "message": "Message", "copy": "Copy", "showing": "Showing", "of": "of", "entries": "entries", "send": "Send"}, "login": {"title": "<PERSON><PERSON>", "welcome_back": "Welcome back", "email": "Email", "password": "Password", "username": "Username", "enter_username": "Enter username", "enter_password": "Enter password", "enter_email": "Enter email address", "username_required": "Username is required", "username_invalid": "<PERSON><PERSON><PERSON> is invalid", "password_required": "Password is required", "email_required": "Email is required", "email_invalid": "<PERSON><PERSON> is invalid", "remember_me": "Remember me", "forgot_password": "Forgot password?", "logging_in": "Logging in...", "no_account": "Don't have an account?", "register_now": "Register now", "or": "or", "continue_with_google": "Continue with Google"}, "signup": {"title": "Register", "create_account": "Create account", "join_community": "Join our community and get access to all services", "full_name": "Full name", "enter_full_name": "Enter full name", "full_name_required": "Full name is required", "email": "Email", "enter_email": "Enter email", "email_required": "Email is required", "email_invalid": "<PERSON><PERSON> is invalid", "username": "Username", "enter_username": "Enter username", "username_required": "Username is required", "password": "Password", "enter_password": "Enter password", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters", "password_no_space": "Password cannot contain spaces", "password_invalid_characters": "Password can only contain letters, numbers and basic special characters", "confirm_password": "Confirm password", "enter_confirm_password": "Confirm your password", "confirm_password_required": "Confirm password is required", "passwords_not_match": "Passwords do not match", "terms_agree": "I agree to the terms and conditions", "terms_required": "You must agree to the terms and conditions", "register": "Register", "already_have_account": "Already have an account?", "login": "<PERSON><PERSON>"}, "forgot_password": {"title": "Forgot password", "description": "Enter your email address and we'll send you a link to reset your password.", "email_sent": "Email sent", "reset_email_sent_to": "Password reset instructions have been sent to", "resend_email": "Resend email", "send_reset_link": "Send reset link", "sending": "Sending...", "back_to_login": "Back to login"}, "reset_password": {"title": "Reset password", "description": "Enter your new password below.", "new_password": "New password", "confirm_password": "Confirm password", "enter_new_password": "Enter new password", "confirm_new_password": "Confirm new password", "password_min_length": "Password must be at least 6 characters", "confirm_password_required": "Confirm password is required", "passwords_do_not_match": "Passwords do not match", "password_reset_success": "Password reset successful", "password_reset_success_description": "Your password has been reset successfully. You can now login with your new password.", "go_to_login": "Go to login", "resetting_password": "Resetting password..."}, "mfa": {"title": "MFA Verification", "instruction": "Please enter the 6-digit code from your authenticator app", "code": "MFA Code", "enter_code": "Enter 6-digit code", "code_required": "MFA code is required", "code_length": "MFA code must be 6 digits", "verify": "Verify", "verifying": "Verifying...", "back_to_login": "Back to login"}, "new_order": {"all_networks": "All networks", "title": "New order", "select_service": "Select service", "order_details": "Order details", "enter_link": "Enter link", "enter_quantity": "Enter quantity", "total_cost": "Total cost", "creating_order": "Creating order...", "min_quantity": "Minimum quantity", "max_quantity": "Maximum quantity", "schedule_and_loop": "Schedule & Loop", "schedule": "Schedule", "your_timezone": "Your timezone", "loop": "Loop", "auto_reorder_when_completed": "Auto reorder", "carefully_use_ensure_balance": "Use this feature carefully. Always ensure sufficient balance", "wait_completion": "Loop when this order is COMPLETED", "loop_quantity": "Loop quantity", "loop_spacing": "Loop spacing", "minutes": "minutes", "comments": "Comments", "enter_comments_one_per_line": "Enter comments, one per line", "voucher_code": "Voucher code", "enter_voucher_code": "Enter voucher code", "voucher_applied": "Voucher applied", "invalid_voucher": "Invalid voucher code", "voucher_expired": "Voucher code expired", "voucher_limit_reached": "Voucher usage limit reached", "order_success": "Order successful", "link_details": "Link details:", "start_time": "Start time", "immediately": "Immediately", "guarantee": "Guarantee", "description": "Description", "speed": "Speed", "average_time": "Average time", "favorite": "Favorite", "warranty": "Warranty", "schedule_time_future_error": "Schedule time must be in the future"}, "mass_order": {"title": "Mass order", "bulk_orders": "Bulk orders", "bulk_description": "Create multiple orders efficiently with the bulk ordering system", "simple_order": "Simple order", "classic_order": "Classic order", "content": "Content", "service": "Service", "quantity": "Quantity", "create_order": "Create order", "list": "List", "category_required": "Please select a category", "service_required": "Please select a service", "content_required": "Please enter content", "content_min_length": "Content must be at least 10 characters", "quantity_required": "Please enter quantity", "quantity_min": "Minimum quantity is", "quantity_max": "Maximum quantity is", "enter_links_one_per_line": "Enter each link on a new line", "enter_quantity": "Enter quantity", "processing": "Processing...", "order_results": "Order results", "success": "Success", "failed": "Failed", "success_status": "✓ Success", "failed_status": "✗ Failed", "format_hint": "Format", "one_order_per_line": "One order per line", "links_count": "Number of links", "total_price": "Total price", "classic_description": "Enter orders in classic format with service ID, link and quantity", "format": "Format", "classic_placeholder": "service_id | link | quantity\nOne order per line", "content_placeholder": "Enter links here, one link per line", "total": "Total", "help_title": "Need help?", "lines": "Lines", "characters": "Characters", "detailed_results": "Detailed results", "result_successful": "Successful", "result_failed": "Failed"}, "orders": {"title": "Orders", "search_orders": "Search orders", "search_placeholder": "Search orders...", "all_orders": "All orders", "hide": "<PERSON>de", "show": "Show", "filters": "Filters", "hide_filters": "Hide filters", "show_filters": "Show filters", "apply": "Apply", "reset_filter": "Reset filter", "all": "All", "in_progress": "In progress", "failed": "Failed", "partial": "Partial", "canceled": "Canceled", "pending": "Pending", "completed": "Completed", "refill": "Refill", "moderation": "Moderation", "serial_number": "No.", "remaining": "Remaining", "notes": "Notes", "reorder": "Reorder", "warranty": "Warranty", "cancel": "Cancel", "started": "Started", "more_actions": "More actions", "remaining_quantity": "Remaining quantity", "copy_id": "Copy ID", "copy_ids": "Copy IDs", "copy_details": "Copy details", "bulk_refill": "Bulk refill", "charge": "Charge", "created": "Created", "no_orders": "No orders found", "no_orders_desc": "You haven't placed any orders yet", "no_orders_found": "No orders found", "category": "Category", "service": "Service", "date": "Date", "showing": "Showing", "of": "of", "entries": "entries", "actions": "Actions", "link": "Link", "status": "Status", "amount": "Amount", "quantity": "Quantity", "order": "Order"}, "nav": {"services": "Services", "new_order": "New order", "mass_order": "Mass order", "orders": "Orders", "dripfeed": "Dripfeed", "warranty": "Warranty", "service_list": "Services", "currency": "<PERSON><PERSON><PERSON><PERSON>", "cash_flow": "Cash flow", "top_up": "Add funds", "others": "Others", "support": "Support", "settings": "Settings", "affiliates": "Affiliates", "update": "Update", "earn_money": "Earn money", "create_website": "Child panel", "api": "API", "extra_pages": "Extra pages", "login": "<PERSON><PERSON>", "register": "Register"}, "auth": {"main_provider": "#1 SMM Service Provider", "cheapest_panel": "Cheapest SMM panel for resellers", "intro_text": "As the SmmProvider family, we are the direct provider of more than 50% of our services. We provide the cheapest SMM panel service with the best support. Join our family and grow together!", "affordable_pricing": "Affordable fixed pricing!", "lowest_reseller": "Guaranteed lowest reseller prices", "multiple_payment": "Multiple payment methods", "high_quality": "High quality services", "support_24_7": "24/7 Support", "orders_per_day": "Over 100,000 orders per day", "in_seconds": "In seconds..", "completed_orders": "Completed orders", "order_every": "Orders processed every", "price_starts": "Prices start from", "our_services": "Our services", "services_count": "services", "why_choose_us": "Why choose us?", "direct_provider": "Direct provider", "direct_provider_text": "We are the direct provider of some SMM services; we use our own database, providing the highest quality of service. We recommend using these services as they support you directly.", "payment_options": "Multiple payment options", "payment_options_text": "We provide individual customers with multiple payment methods, including credit cards, bank transfers, Google Pay, Payeer, Perfect Money, WebMoney and cryptocurrencies like Bitcoin, Litecoin and USDT.", "affordable_prices": "Affordable prices", "affordable_prices_text": "We provide high-quality social media services at affordable prices in a competitive and demanding industry. Our prices start from just $0.0001/1000, making us the cheapest SMM panel provider in the market.", "api_integration": "API integration", "api_integration_text": "We provide API integration for all our SMM services, allowing you to automate orders and integrate our panel features into your website or bot for free.", "instant_delivery": "Instant delivery", "instant_delivery_text": "If you're looking for the fastest social media service, SmmProvider is the perfect choice. Your orders start immediately and will be delivered as soon as possible according to quantity.", "support_24_7_title": "24/7 Support", "support_24_7_text": "Our SMM panel has a 24/7 customer support team ready to help you with any questions. We never ignore or miss any of your messages until everything is resolved!", "faq": "Frequently Asked Questions", "faq_what_is_smm": "What is an SMM panel?", "faq_what_is_smm_answer": "An SMM panel (Social Media Marketing) is a platform that allows you to buy social media services like likes, followers, views and many other services for various social media platforms.", "faq_what_is_smm_service": "What is a social media marketing service?", "faq_what_is_smm_service_answer": "Social media marketing services include services that help increase presence and engagement on social media platforms, such as increasing followers, likes, views and comments.", "faq_best_smm_vietnam": "What is the best and cheapest SMM panel in Vietnam?", "faq_best_smm_vietnam_answer": "Our SMM service provides the best and cheapest SMM panel in Vietnam with competitive prices and high-quality services.", "faq_best_provider": "Who provides the best SMM services?", "faq_best_provider_answer": "With years of experience and commitment to quality, we are proud to be the best SMM service provider with 24/7 support and competitive prices.", "faq_cheapest_panel": "What is the cheapest SMM panel?", "faq_cheapest_panel_answer": "The cheapest SMM panel is a platform that provides social media services at the lowest prices in the market while still ensuring quality. Our services start from just $0.0001/1000.", "faq_payment_methods": "What are the payment methods for SMM Panel?", "faq_payment_methods_answer": "We accept multiple payment methods such as credit cards (Visa), Paytm, Perfect Money, Payeer, Binance Pay and various other cryptocurrencies.", "payment_methods": "Payment methods", "google_authentication_setting": "Google authentication setup", "get_google_authenticator": "Get Google Authenticator on your phone", "get_on_android": "Get on Android", "get_on_ios": "Get on iOS", "scan_qr_code_instruction": "Scan the QR code. It will generate a 6-digit code for you to enter below.", "manual_entry_instruction": "If you have trouble using the QR code, select manual entry in the app and enter username and code:", "enter_authentication_code": "Enter authentication code", "code_required": "Authentication code is required", "code_must_be_6_digits": "Code must be 6 digits", "enable": "Enable", "disable": "Disable", "disable_two_factor_auth": "Disable two-factor authentication", "disable_two_factor_auth_confirmation": "Please enter your password to disable two-factor authentication.", "disable_two_factor_auth_warning": "Warning: Disabling two-factor authentication will make your account less secure. Anyone with your password will be able to access your account.", "register": "Register", "services": "Services", "username": "Username", "enter_username": "Enter username", "username_required": "Username is required", "username_invalid": "<PERSON><PERSON><PERSON> is invalid", "password": "Password", "forgot_password": "Forgot password?", "enter_password": "Enter password", "password_required": "Password is required", "remember_me": "Remember me", "login": "<PERSON><PERSON>", "logging_in": "Logging in...", "forgot_password_description": "Enter your email address and we'll send you a link to reset your password.", "email": "Email", "reset_email_sent_to": "Password reset instructions have been sent to", "resend_email": "Resend email", "send_reset_link": "Send reset link", "sending": "Sending...", "email_sent": "Email sent", "back_to_login": "Back to login", "reset_password": "Reset password", "reset_password_description": "Enter your new password below.", "new_password": "New password", "confirm_password": "Confirm password", "enter_new_password": "Enter new password", "confirm_new_password": "Confirm new password", "password_min_length": "Password must be at least 6 characters long", "confirm_password_required": "Confirm password is required", "passwords_do_not_match": "Passwords do not match", "password_reset_success": "Password reset successful", "password_reset_success_description": "Your password has been reset successfully. You can now login with your new password.", "go_to_login": "Go to login", "resetting_password": "Resetting password..."}, "header": {"home": "Home", "services": "Services", "pricing": "Pricing", "grocery": "Grocery", "resources": "Resources", "blog": "Blog", "contact": "Contact", "login": "<PERSON><PERSON>", "register": "Register"}, "footer": {"about_us": "About us", "about_text": "SmmProvider is a leading SMM service provider, offering high-quality social media services at affordable prices.", "quick_links": "Quick links", "home": "Home", "services": "Services", "pricing": "Pricing", "blog": "Blog", "contact": "Contact", "support": "Support", "faq": "FAQ", "terms": "Terms", "privacy": "Privacy Policy", "contact_us": "Contact us", "address": "Address", "address_text": "123 Nguyen Hue Street, District 1, Ho Chi Minh City", "phone": "Phone", "email": "Email", "copyright": "© 2025 SmmProvider. All rights reserved.", "popular_services": "Popular services", "help_center": "Help center"}, "api_doc": {"title": "API Documentation", "service": "Service", "api_url": "API URL", "api_key": "API Key", "https_method": "HTTPS Method", "content_type": "Content Type", "responsive": "Response", "parameter": "Parameter", "description": "Description", "example_responsive": "Example response", "mass_order": "Mass order", "orders": "Orders", "warranty": "Warranty", "service_list": "Service list", "synchronize": "Synchronize", "top_up": "Top up", "support": "Support", "questions": "Questions", "update": "Update", "earn_money": "Earn money", "create_website": "Create website"}, "services": {"refill": "Refill", "title": "Services", "search_placeholder": "Search services...", "search_by_name_or_id": "Search by name or ID", "price": "Price", "min_order": "Min order", "max_order": "Max order", "avg_time": "Avg time", "refill_available": "Refill available", "cancel_available": "Cancel available", "order_now": "Order now", "no_services": "No services found", "no_services_desc": "No services available for selected criteria", "no_service_selected": "No service selected", "select_service_to_view_info": "Select a service to view details", "no_service_found": "No matching service found", "favorite_services": "Favorite services", "no_favorite_services": "No favorite services found", "no_services_available": "No services available", "select_platform_first": "Please select a platform first", "information": "Service information", "warranty": "Warranty", "example_url": "Example URL", "min_value": "Minimum", "max_value": "Maximum", "exchange_rate": "Exchange rate", "average_time": "Average time", "select_platform": "Select platform", "all_networks": "All networks", "facebook": "Facebook", "instagram": "Instagram", "youtube": "YouTube", "auto_subscription": "Auto subscription", "classification": "Classification", "order_limit": "Order limit", "charge_fee": "Charge fee", "start_time": "Start time", "immediately": "Immediately", "guarantee": "Guarantee", "refund_period": "Refund period", "speed": "Speed", "growth_rate": "Growth rate", "processing_time": "Processing time"}, "child_panel": {"create_child_panel": "Create child panel", "my_panels": "My Panels", "no_panels": "You don't have any panels yet", "domain": "Domain", "status": "Status", "created_at": "Created At", "actions": "Actions", "open": "Open", "pending": "Pending", "status_active": "Active", "status_waiting": "Waiting", "status_suspended": "Suspended", "status_new": "New", "status_unknown": "Unknown", "domain_name": "Domain name", "exchange_rate": "Exchange rate", "create_website": "Create website", "admin_name": "Admin name", "admin_password": "Admin password", "confirm_admin_password": "Confirm admin password", "retail_price": "Retail price", "payment": "Payment", "domain_setup_guide": "If you have a domain, you just need to change the name server...", "search_orders": "Search orders", "go_button": "Go", "copy_id": "Copy ID", "refill": "Refill", "title": "Child panel", "description": "Create your own website with Child panel", "create_now": "Create now", "features": "Features", "feature_1": "Create your own website with your domain", "feature_2": "Customize interface and logo", "feature_3": "Manage orders and customers", "feature_4": "API integration", "feature_5": "24/7 technical support", "feature_6": "High security", "domain_setup_info": "If you have a domain, you just need to change the nameserver for your domain and point it to:", "faq_title": "Frequently Asked Questions", "faq_1_question": "What is an SMM panel?", "faq_1_answer": "An SMM panel is an online platform where you can buy various social media marketing services to enhance your clients' online presence.", "faq_2_question": "What is a child panel?", "faq_2_answer": "A child panel is a version of the SMM panel that you can own and manage. It allows you to resell SMM services under your own brand.", "faq_3_question": "What SMM services are available?", "faq_3_answer": "We offer various SMM services such as followers, likes, views, comments on popular social media platforms like Facebook, Instagram, YouTube, TikTok, Twitter and many other platforms.", "faq_4_question": "How to manage SMM panel?", "faq_4_answer": "You can manage the SMM panel through the admin dashboard, where you can add services, manage orders, track payments and manage users.", "faq_5_question": "How to find customers?", "faq_5_answer": "You can find customers through online marketing, social media, affiliate marketing, SEO and paid advertising. Building relationships with resellers and influencers can also help you expand your customer base.", "faq_6_question": "How to find suppliers?", "faq_6_answer": "You can find SMM service suppliers through online forums, Facebook groups and SMM marketplaces. Make sure to research thoroughly and check reviews before choosing a supplier."}, "ticket": {"title": "Support", "support": "Support", "create_ticket": "Create support ticket", "search": "Search", "search_placeholder": "Search by ID or description...", "select_all": "Select all", "id": "ID", "subject": "Subject", "status": "Status", "last_update": "Last update", "customer": "Customer", "ticket_details": "TICKET DETAILS", "ticket_id": "TICKET ID", "request": "REQUEST", "priority": "PRIORITY", "department": "DEPARTMENT", "response_time": "RESPONSE TIME", "reply_now": "REPLY NOW", "type_message": "Type your message here...", "auto_reset": "Auto reset after", "new_ticket": "New ticket", "order": "Order", "payment": "Payment", "point": "Point", "other": "Other", "orders_id": "ORDER ID", "refill": "Refill", "cancel": "Cancel", "speed_up": "Speed up", "message": "MESSAGE", "submit_now": "SUBMIT NOW", "created_at": "Created at", "updated_at": "Updated at", "note": "Note", "user": "User", "no_tickets": "No tickets found", "error": "Error", "api_timeout": "API timeout when updating messages", "api_timeout_load": "API timeout when loading ticket data", "sending": "Sending", "closed": "Ticket closed", "closed_message": "This ticket has been closed. You cannot send any more messages.", "pending_message": "Your ticket is pending. The support team will review it soon.", "accept_message": "Your ticket has been accepted. Our team is working on it.", "solved_message": "Your ticket has been resolved. Please let us know if you need further assistance."}, "add_fund": {"title": "Add funds", "payment_note": "Note: Payment may take 5-15 minutes to process. Contact support via Telegram for example", "member_rank": "Member rank", "total_deposit": "Total deposit", "total_bonus": "Total bonus", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "payment_method": "Select your preferred payment method", "scan_qr": "Scan QR code to pay", "transfer_to": "Transfer to", "to": "to", "account_number": "Account number", "account_name": "Account name", "transfer_content": "Transfer content", "crypto": "Cryptocurrency", "register_marketplace": "Register to connect with marketplace website", "amount": "Amount", "custom_amount": "Custom amount", "pay": "Pay", "transaction_history": "Transaction history", "transaction_id": "Transaction ID", "date": "Date", "type": "Type", "method": "Method", "transaction_amount": "Amount", "select_payment_method": "Select payment method", "search_payment_methods": "Search payment methods...", "no_payment_methods_found": "No payment methods found", "bonus_conditions": "Bonus conditions", "min_amount": "Minimum amount", "bonus_amount": "Bonus amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "from_date": "From date", "to_date": "To date", "date_range": "Date range", "no_transactions": "No transactions found", "bank": "Bank", "account": "Account number", "name": "Name", "content": "Content", "copy_account": "Copy account number", "copy_content": "Copy content", "transfer_amount": "Transfer amount", "bank_info": "Bank information", "bank_name": "Bank name", "transfer_content_note": "Please enter this content exactly when making the transfer", "amount_limits": "Amount limits"}, "cash_flow": {"title": "Cash flow", "subtitle": "Track your transaction history", "total_transactions": "Total transactions", "cash_flow_diary": "Cash flow diary", "all_transactions": "All transactions", "transaction_type": "Transaction type", "order_id": "Order ID", "no_transactions": "No transactions found", "id": "No.", "link": "Link", "total_amount": "Total amount", "cash_flow": "Cash flow", "description": "Description", "reset": "Reset", "no_transactions_found": "No transactions found", "no_transactions_found_desc": "You don't have any transactions yet", "date_range": "Date range"}, "notifications": {"title": "Notifications", "dismiss": "<PERSON><PERSON><PERSON>", "close": "Close", "mark_as_read": "<PERSON> as read", "new_announcement": "New announcement", "no_more_display": "Viewed", "unread": "unread", "unread_filter": "Unread", "all_filter": "All", "loading": "Loading...", "no_notifications": "No notifications", "no_unread_notifications": "No unread notifications", "view_all": "View all notifications"}, "update": {"title": "Update", "search": "Search", "service": "Service", "time": "Time", "status": "Status", "all": "All", "price_increase": "Price increase", "price_decrease": "Price decrease", "new": "New", "on": "On", "off": "Off", "showing": "Showing", "of": "of", "entries": "entries", "show": "Show", "no_data": "No update logs found", "refresh": "Refresh", "from": "from", "to": "to"}, "affiliates": {"referral_commission": "Referral commission", "affiliate_program": "Affiliate program", "affiliates": "Affiliates", "payment_history": "Payment history", "referral_link": "Referral link:", "copy_your_link": "COPY YOUR LINK", "copied": "COPIED!", "commission_rate": "Commission rate", "affiliate_system": "Affiliate system", "loyalty_program": "Loyalty program and percentage rates", "enable_on_panel": "Enable on panel", "set_percentage": "Set referral income percentage", "save": "Save", "total_referrals": "Total referrals", "commission_earnings": "Commission earnings", "successful_referrals": "Successful referrals", "no_referrals": "No referrals found", "share_link": "Share referral link to start earning commissions", "payment_history_empty": "Your affiliate payment history will appear here", "program_description": "Earn money by referring new users to our platform. You will receive commission for each purchase they make.", "share_link_info": "Share this link with friends, on social media or on your website to earn commissions.", "per_referral": "per referral", "active_users": "active users", "total_earned": "total earned", "current_balance": "current balance", "commission_info": "Commission is calculated based on referred user's deposit transactions", "invite_friends": "Invite friends", "payment_history_description": "Track all your affiliate earnings and payment transactions in one place.", "no_transactions": "No transactions yet", "request_payment": "Request payment", "view_requirements": "View requirements", "enable_affiliate": "Enable affiliate system", "percentage_affiliate": "Affiliate percentage", "default_rate": "Default rate", "custom_rate": "Custom rate", "pending_commissions": "Pending commissions", "paid_commissions": "Paid commissions", "commission_status": "Commission status", "pending": "Pending", "paid": "Paid", "referral_code": "Referral code", "referred_user": "Referred user", "transaction_note": "Transaction note", "commission_amount": "Commission amount", "user_referrals": "User referrals", "referral_stats": "Referral stats", "my_referrals": "My referrals", "my_commissions": "My commissions", "affiliate_link": "Affiliate link", "effective_rate": "Effective rate", "using_custom_rate": "Using custom rate", "using_default_rate": "Using tenant default rate", "transaction": "Transaction", "transaction_id": "Transaction ID", "transaction_details": "Transaction details", "transaction_history": "Transaction history", "transaction_amount": "Transaction amount", "transaction_date": "Transaction date", "transaction_status": "Transaction status", "transaction_type": "Transaction type", "transaction_reference": "Transaction reference", "view_transaction": "View transaction", "transaction_summary": "Transaction summary", "recent_transactions": "Recent transactions", "all_time": "All time", "awaiting_payment": "Awaiting payment", "total_received": "Total received", "commission_payments": "Commission payments", "requirements": "Requirements", "pending_payments": "Pending payments", "total_commissions": "Total commissions"}, "landing": {"header": {"services": "Services", "features": "Features", "pricing": "Pricing", "login": "<PERSON><PERSON>", "signup": "Sign up"}, "hero": {"title": "The most convenient SMM provider", "subtitle": "We will make you famous with high-quality social media services", "signup_button": "Sign up now", "dashboard_button": "Dashboard"}, "stats": {"happy_customers": "Thousands of happy customers", "completed_orders": "Millions of completed orders", "quality_services": "High-quality services"}, "services": {"title": "Huge service inventory", "subtitle": "On all popular social networks", "view_all": "View all services", "followers": "Followers", "likes": "<PERSON>s", "views": "Views"}, "features": {"title": "We have everything you need", "subtitle": "High-quality services, convenient panel, great support and our API", "stats": "Real-time statistics", "stats_desc": "Track orders and account activity with detailed analytics and reports.", "payments": "Secure payments", "payments_desc": "Multiple payment methods with secure processing and transaction history.", "api": "API integration", "api_desc": "Easily integrate our services with your application using our powerful API.", "support": "24/7 Support", "support_desc": "Dedicated support team always ready to help you with any questions."}, "quality": {"title": "Great quality and affordable prices", "amazing_quality": "Amazing quality", "amazing_quality_desc": "We monitor our services at all times. If quality doesn't meet expectations, you'll get a full refund.", "affordable_prices": "Affordable prices", "affordable_prices_desc": "Our panel only works with direct providers, you get the cheapest prices from over 400 items.", "get_started": "Get started", "price_prefix": "$"}, "footer": {"slogan": "The most convenient SMM provider", "copyright": "© 2023 NEWPANEL. All rights reserved.", "terms": "Terms of service", "privacy": "Privacy policy", "contact": "Contact"}}, "profile": {"title": "Profile", "information": "Information", "email": "Email", "phone_number": "Phone number", "current_password": "Current password", "new_password": "New password", "confirm_password": "Confirm password", "update": "Update", "updating": "Updating...", "update_password": "Update password", "updating_password": "Updating password...", "change_password": "Change password", "verify": "Verify", "verifying": "Verifying...", "already_verified": "Already verified", "email_required": "Email is required", "email_invalid": "<PERSON><PERSON> is invalid", "phone_required": "Phone number is required", "current_password_required": "Current password is required", "new_password_required": "New password is required", "confirm_password_required": "Confirm password is required", "password_min_length": "Password must be at least 8 characters", "password_no_space": "Password cannot contain spaces", "password_invalid_characters": "Password contains invalid characters", "passwords_not_match": "Passwords do not match", "password_updated": "Password updated successfully", "password_error": "Unable to update password", "profile_updated": "Profile updated successfully", "profile_error": "Unable to update profile", "account": "Account", "online": "Online", "balance": "Balance", "orders": "Orders", "status": "Status", "successful": "Successful", "page": "Page", "of": "of", "linked_accounts": "Linked accounts", "link_telegram_to_receive_notifications": "Link your Telegram account to receive important notifications.", "link_telegram": "Link with Telegram", "telegram_username": "Telegram username", "open_telegram_bot": "Open Telegram Bot", "telegram_guide": "Connection guide", "security": "Security", "settings": "Settings", "login_history_tab": "Login history", "two_factor_auth": "Two-factor authentication", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "appearance": "Appearance", "timezone": "Timezone", "login_history": "Login history", "loading_history": "Loading history...", "no_login_history": "No login history", "time": "Time", "ip": "IP address", "device": "<PERSON><PERSON>", "login_records": "login records", "two_factor_auth_description": "Add an extra layer of security to your account by requiring a verification code in addition to your password.", "enable": "Enable", "telegram_guide_popup": {"title": "🔧 Guide to connect account with Telegram bot", "step1_title": "Step 1: Get API Key", "step1_content": ["Go to /dashboard/apidoc page.", "Find the API Key section.", "Click on the icon (2 rotating arrows) to create a new API Key.", "Copy the newly created API Key for use in the next step."], "step2_title": "Step 2: Open Telegram bot", "step2_content": ["Go to /dashboard/settings page, select Account tab.", "Click \"Link with Telegram\" button.", "This action will automatically open Telegram app and take you to the system bot."], "step3_title": "Step 3: Authenticate with bot", "step3_content": ["In the chat with Telegram bot, send a message with the syntax:", "/auth <APIKEY>", "Where <APIKEY> is the code you copied in step 1.", "Example: /auth 1234abcd5678efgh"], "complete_title": "✅ Complete", "complete_content": ["If the API Key is valid, the bot will report successful authentication.", "You have successfully linked your account and can start using features via Telegram bot."]}, "email_verification": {"title": "Email verification", "sent_title": "Verification email sent!", "sent_message": "We have sent a verification email to:", "instructions": "Please follow these steps to complete verification:", "step1": "Check your inbox", "step2": "Find the email from us with subject 'Verify email address'", "step3": "Click the verification link in the email", "note": "If you don't see the email, please check your spam or junk folder.", "resend": "Resend email", "verifying": "Verifying email...", "please_wait": "Please wait while we verify your email.", "success_title": "Email verified successfully!", "success_message": "Your account has been verified. You can login now.", "go_to_login": "Go to login", "error_title": "Email verification failed", "error_message": "There was an error verifying your email. Please try again later.", "go_to_home": "Go to home"}}, "settings": {"title": "Settings", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "appearance": "Appearance", "timezone": "Timezone", "login_history": "Login history", "login_history_tab": "Login history", "loading_history": "Loading history...", "no_login_history": "No login history", "time": "Time", "ip": "IP address", "device": "<PERSON><PERSON>", "login_records": "login records", "security": "Security", "two_factor_auth": "Two-factor authentication", "two_factor_auth_description": "Add an extra layer of security to your account by requiring a verification code in addition to your password.", "enable": "Enable", "disable": "Disable", "enabled": "Enabled", "disabled": "Disabled", "api_key": "API Key", "api_key_description": "Use this key to access our API services.", "generate": "Generate", "regenerate": "Regenerate", "copy": "Copy", "copied": "Copied!", "never_share": "Never share your API key with anyone.", "last_generated": "Last generated", "interface_style": "Interface style", "choose_interface_description": "Choose your preferred interface style", "interface_saved_automatically": "Interface options are saved automatically"}, "dripfeed": {"title": "Dripfeed management", "service": "Service", "link": "Link", "quantity": "Quantity", "schedule": "Schedule", "loop_settings": "Loop settings", "status": "Status", "reason": "Reason", "orders": "Orders", "actions": "Actions", "no_dripfeeds": "No dripfeeds found", "no_dripfeeds_description": "You haven't created any scheduled orders yet.", "create_first": "Create your first order", "search_placeholder": "Search by link or ID...", "stop": "Stop"}, "home": {"welcome_to": "Welcome to", "completed_orders": "Completed orders", "account_balance": "Account balance", "account_spent": "Account spent"}, "transaction_types": {"order": "Order", "spent": "Spent", "bonus": "Bonus", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "refund": "Refund", "all": "All transaction types"}, "favorites": {"title": "Favorites", "favorite_services": "Favorite services", "no_favorites": "No favorite services found", "min_value": "Minimum value", "max_value": "Maximum value", "exchange_rate": "Exchange rate", "average_time": "Average time"}, "filter": {"all_platforms": "All platforms", "all_categories": "All categories", "all_services": "All services", "button": "Filter", "select_category": "Select category", "select_service": "Select service", "select_platform": "Select platform"}, "time": {"hours": "hours", "minutes": "minutes", "seconds": "seconds"}, "avatar": {"agent": "Agent", "account_info": "Account info", "change_password": "Change password", "balance": "Balance", "total_orders": "Total orders", "logout": "Logout"}, "error": {"not_found_title": "Page not found", "not_found_message": "The page you are looking for does not exist or has been moved.", "unauthorized_title": "Unauthorized access", "unauthorized_message": "You need to login to access this page.", "forbidden_title": "Access forbidden", "forbidden_message": "You do not have permission to access this page.", "server_error_title": "Server error", "server_error_message": "There is a problem with the server. Please try again later.", "go_back": "Go back", "go_home": "Go home", "login": "<PERSON><PERSON>", "refresh": "Refresh page"}, "simple_theme": {"auth": {"login": "<PERSON><PERSON>", "welcome_back": "Welcome back", "username": "Username", "enter_username": "Enter username", "password": "Password", "enter_password": "Enter password", "username_required": "Username is required", "username_invalid": "Invalid username format", "password_required": "Password is required", "remember_me": "Remember me", "forgot_password": "Forgot password?", "logging_in": "Logging in...", "no_account": "Don't have an account?", "register_now": "Register now", "completed_orders": "Completed orders", "support": "Support", "price_starts": "Prices start"}, "new_order": {"new_order": "New order", "schedule_and_loop": "Schedule & Loop", "schedule": "Schedule", "your_timezone": "Your timezone", "loop": "Loop", "auto_reorder_when_completed": "Auto reorder", "carefully_use_ensure_balance": "Use this feature carefully. Always ensure sufficient balance", "wait_completion": "when this order is COMPLETED", "loop_quantity": "Loop quantity", "loop_spacing": "Loop spacing", "minutes": "minutes"}, "orders": {"title": "Orders", "search_placeholder": "Search orders...", "selected": "selected", "copy_ids": "Copy IDs", "copy_details": "Copy details", "bulk_refill": "Bulk refill", "id": "ID", "service": "Service", "link": "Link", "quantity": "Quantity", "charge": "Charge", "status": "Status", "created": "Created", "no_orders": "No orders found", "no_orders_desc": "You haven't placed any orders yet"}, "services": {"title": "Services", "search_placeholder": "Search services...", "services": "services", "price": "Price", "min_order": "Min order", "max_order": "Max order", "avg_time": "Avg time", "refill_available": "Refill available", "cancel_available": "Cancel available", "order_now": "Order now", "no_services": "No services found", "no_services_desc": "No services available for selected criteria", "information": "Service information", "warranty": "Warranty", "no_service_selected": "No service selected", "select_service_to_view_info": "Select a service to view details", "no_service_found": "No matching service found"}, "cash_flow": {"title": "Cash flow", "subtitle": "Track your transaction history", "total_transactions": "Total transactions"}, "platforms": {"select": "Select platform"}, "mass_order": {"bulk_orders": "Bulk orders", "bulk_description": "Create multiple orders efficiently with the bulk ordering system", "help_title": "Need help?", "lines": "Lines", "characters": "Characters", "detailed_results": "Detailed results", "result_successful": "Successful", "result_failed": "Failed"}, "common": {"loading": "Loading", "error": "Error", "redirecting": "Redirecting", "created": "Created", "updated": "Updated", "filter": "Filter", "search": "Search", "date_range": "Date range", "category": "Category", "service": "Service", "platform": "Platform", "reset": "Reset", "apply": "Apply", "actions": "Actions", "close": "Close", "sending": "Sending...", "go_to_dashboard": "Go to Dashboard"}, "filter": {"select_category": "Select category", "select_service": "Select service", "select_platform": "Select platform"}}}