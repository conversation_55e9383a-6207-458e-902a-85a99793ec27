{"datetime_picker": {"select_time": "<PERSON><PERSON><PERSON> waktu", "selected_date": "<PERSON><PERSON> yang dipilih", "cancel": "<PERSON><PERSON>", "apply": "Terapkan"}, "common": {"loading": "Memuat", "error": "<PERSON><PERSON><PERSON>", "redirecting": "Mengalihkan", "created": "Dibuat", "updated": "<PERSON><PERSON><PERSON><PERSON>", "filter": "Filter", "search": "<PERSON><PERSON>", "date_range": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "service": "<PERSON><PERSON><PERSON>", "platform": "Platform", "reset": "Reset", "apply": "Terapkan", "actions": "<PERSON><PERSON><PERSON>", "sending": "Mengirim...", "go_to_dashboard": "Ke Dashboard", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "save": "Simpan", "edit": "Edit", "delete": "Hapus", "view": "Lihat", "no_data": "Tidak ada data", "select_all": "<PERSON><PERSON><PERSON> se<PERSON>a", "status": "Status", "user": "Pengguna", "link": "Tautan", "quantity": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "total_amount": "Total jumlah", "description": "<PERSON><PERSON><PERSON><PERSON>", "note": "Catatan", "example": "<PERSON><PERSON><PERSON>", "selected": "dipilih", "information": "Informasi", "email": "Email", "password": "<PERSON>a sandi", "username": "<PERSON><PERSON>", "full_name": "<PERSON><PERSON> le<PERSON>", "phone_number": "Nomor telepon", "balance": "<PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "date": "Tanggal", "time": "<PERSON><PERSON><PERSON>", "id": "ID", "name": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "content": "Konten", "subject": "Subjek", "message": "<PERSON><PERSON>", "copy": "<PERSON><PERSON>", "showing": "Menampilkan", "of": "dari", "entries": "entri", "send": "<PERSON><PERSON>"}, "login": {"title": "<PERSON><PERSON><PERSON>", "welcome_back": "Selamat datang kembali", "email": "Email", "password": "<PERSON>a sandi", "username": "<PERSON><PERSON>", "enter_username": "<PERSON><PERSON><PERSON><PERSON> nama pengguna", "enter_password": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "enter_email": "<PERSON><PERSON><PERSON><PERSON> email", "username_required": "<PERSON>a pengguna diperlukan", "username_invalid": "Nama pengguna tidak valid", "password_required": "<PERSON>a sandi <PERSON>an", "email_required": "<PERSON><PERSON>", "email_invalid": "<PERSON><PERSON> tidak valid", "remember_me": "<PERSON>gat saya", "forgot_password": "Lupa kata sandi?", "logging_in": "Masuk...", "no_account": "Tidak punya akun?", "register_now": "<PERSON><PERSON><PERSON>", "or": "atau", "continue_with_google": "Lanjutkan dengan Google"}, "signup": {"title": "<PERSON><PERSON><PERSON>", "create_account": "<PERSON><PERSON><PERSON> akun", "join_community": "Bergabunglah dengan komunitas kami dan dapatkan akses ke semua layanan", "full_name": "<PERSON><PERSON> le<PERSON>", "enter_full_name": "<PERSON><PERSON><PERSON><PERSON> nama lengkap", "full_name_required": "<PERSON><PERSON> le<PERSON> diperlukan", "email": "Email", "enter_email": "Masukkan email", "email_required": "<PERSON><PERSON>", "email_invalid": "<PERSON><PERSON> tidak valid", "username": "<PERSON><PERSON>", "enter_username": "<PERSON><PERSON><PERSON><PERSON> nama pengguna", "username_required": "<PERSON>a pengguna diperlukan", "password": "<PERSON>a sandi", "enter_password": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "password_required": "<PERSON>a sandi <PERSON>an", "password_min_length": "Kata sandi harus minimal 8 karakter", "password_no_space": "Kata sandi tidak boleh mengandung spasi", "password_invalid_characters": "<PERSON>a sandi hanya boleh mengandung huruf, an<PERSON><PERSON>, dan karakter k<PERSON> dasar", "confirm_password": "Konfirmasi kata sandi", "enter_confirm_password": "Konfirmasi kata sandi <PERSON>a", "confirm_password_required": "Kon<PERSON>rma<PERSON> kata sandi dip<PERSON>an", "passwords_not_match": "Kata sandi tidak cocok", "terms_agree": "<PERSON><PERSON> setuju dengan syarat dan ketentuan", "terms_required": "<PERSON>a harus menyetujui syarat dan ketentuan", "register": "<PERSON><PERSON><PERSON>", "already_have_account": "Sudah punya akun?", "login": "<PERSON><PERSON><PERSON>"}, "forgot_password": {"title": "<PERSON>pa kata sandi", "description": "<PERSON><PERSON><PERSON><PERSON> email Anda dan kami akan mengirimkan tautan untuk mereset kata sandi Anda.", "email_sent": "<PERSON><PERSON> te<PERSON>", "reset_email_sent_to": "Instruksi reset kata sandi telah dikirim ke", "resend_email": "<PERSON><PERSON> email", "send_reset_link": "<PERSON><PERSON> reset", "sending": "Mengirim...", "back_to_login": "<PERSON><PERSON><PERSON> ke masuk"}, "reset_password": {"title": "Reset kata sandi", "description": "<PERSON><PERSON>kkan kata sandi baru Anda di bawah ini.", "new_password": "Kata sandi baru", "confirm_password": "Konfirmasi kata sandi", "enter_new_password": "<PERSON><PERSON>kkan kata sandi baru", "confirm_new_password": "Konfirmasi kata sandi baru", "password_min_length": "<PERSON><PERSON> sandi harus minimal 6 karakter", "confirm_password_required": "Kon<PERSON>rma<PERSON> kata sandi dip<PERSON>an", "passwords_do_not_match": "Kata sandi tidak cocok", "password_reset_success": "Reset kata sandi ber<PERSON>il", "password_reset_success_description": "Kata sandi Anda telah berhasil direset. Anda sekarang dapat masuk dengan kata sandi baru Anda.", "go_to_login": "<PERSON> halaman masuk", "resetting_password": "Mereset kata sandi..."}, "mfa": {"title": "Verifikasi MFA", "instruction": "<PERSON><PERSON><PERSON> masukkan kode 6 digit dari aplikasi authenticator <PERSON><PERSON>", "code": "Kode MFA", "enter_code": "Masukkan kode 6 digit", "code_required": "Kode MFA diperlukan", "code_length": "Kode MFA harus 6 digit", "verify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verifying": "Memverifikasi...", "back_to_login": "<PERSON><PERSON><PERSON> ke masuk"}, "new_order": {"all_networks": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> baru", "select_service": "<PERSON><PERSON><PERSON>n", "order_details": "<PERSON>ail pesanan", "enter_link": "<PERSON><PERSON><PERSON><PERSON>an", "enter_quantity": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "total_cost": "Total biaya", "creating_order": "Me<PERSON><PERSON>t pesanan...", "min_quantity": "<PERSON><PERSON><PERSON>", "max_quantity": "<PERSON><PERSON><PERSON>", "schedule_and_loop": "Jadwal & Loop", "schedule": "<PERSON><PERSON><PERSON>", "your_timezone": "Zona waktu Anda", "loop": "Loop", "auto_reorder_when_completed": "<PERSON><PERSON> ulang otomatis", "carefully_use_ensure_balance": "<PERSON><PERSON><PERSON> fitur ini dengan hati-hati. <PERSON><PERSON><PERSON> pastikan saldo men<PERSON>pi", "wait_completion": "Loop ketika pesanan ini SELESAI", "loop_quantity": "Jumlah loop", "loop_spacing": "Jarak loop", "minutes": "menit", "comments": "Komentar", "enter_comments_one_per_line": "<PERSON><PERSON><PERSON><PERSON> komentar, satu per baris", "voucher_code": "Kode voucher", "enter_voucher_code": "Masukkan kode voucher", "voucher_applied": "Voucher diterapkan", "invalid_voucher": "Kode voucher tidak valid", "voucher_expired": "Kode voucher ked<PERSON><PERSON>warsa", "voucher_limit_reached": "Batas penggunaan voucher tercapai", "order_success": "<PERSON><PERSON><PERSON>", "link_details": "Detail tautan:", "start_time": "<PERSON><PERSON><PERSON> mulai", "immediately": "<PERSON><PERSON><PERSON>", "guarantee": "Jaminan", "description": "<PERSON><PERSON><PERSON><PERSON>", "speed": "Kecepatan", "average_time": "<PERSON><PERSON><PERSON> rata-rata", "favorite": "<PERSON><PERSON><PERSON><PERSON>", "warranty": "<PERSON><PERSON><PERSON>", "schedule_time_future_error": "<PERSON><PERSON><PERSON> jadwal harus di masa depan"}, "mass_order": {"title": "<PERSON><PERSON><PERSON> massal", "bulk_orders": "<PERSON><PERSON><PERSON> massal", "bulk_description": "Buat beberapa pesanan secara efisien dengan sistem pesanan massal", "simple_order": "<PERSON><PERSON><PERSON> se<PERSON>", "classic_order": "<PERSON><PERSON><PERSON> k<PERSON>ik", "content": "Konten", "service": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "create_order": "<PERSON><PERSON><PERSON> pesanan", "list": "<PERSON><PERSON><PERSON>", "category_required": "<PERSON><PERSON><PERSON> pilih kategori", "service_required": "<PERSON><PERSON><PERSON> pilih layanan", "content_required": "<PERSON><PERSON><PERSON> masukkan konten", "content_min_length": "Konten harus minimal 10 karakter", "quantity_required": "<PERSON><PERSON><PERSON> masukkan jumlah", "quantity_min": "<PERSON><PERSON><PERSON> adalah", "quantity_max": "<PERSON><PERSON><PERSON> maksim<PERSON> ad<PERSON>h", "enter_links_one_per_line": "<PERSON><PERSON><PERSON><PERSON> setiap tautan di baris baru", "enter_quantity": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "processing": "Memproses...", "order_results": "<PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON>", "failed": "Gaga<PERSON>", "success_status": "✓ <PERSON><PERSON><PERSON><PERSON>", "failed_status": "✗ Gagal", "format_hint": "Format", "one_order_per_line": "Satu pesanan per baris", "links_count": "<PERSON><PERSON><PERSON>", "total_price": "Total harga", "classic_description": "Ma<PERSON>kkan pesanan dalam format klasik dengan <PERSON>anan, tautan dan jumlah", "format": "Format", "classic_placeholder": "service_id | tautan | jumlah\n<PERSON> pesanan per baris", "content_placeholder": "<PERSON><PERSON><PERSON><PERSON> tautan di sini, satu tautan per baris", "total": "Total", "help_title": "Butuh bantuan?", "lines": "<PERSON><PERSON>", "characters": "<PERSON><PERSON><PERSON>", "detailed_results": "Hasil detail", "result_successful": "<PERSON><PERSON><PERSON><PERSON>", "result_failed": "Gaga<PERSON>"}, "orders": {"title": "<PERSON><PERSON><PERSON>", "search_orders": "<PERSON><PERSON> pesanan", "search_placeholder": "<PERSON>i pesanan...", "all_orders": "<PERSON><PERSON><PERSON>", "hide": "Sembunyikan", "show": "<PERSON><PERSON><PERSON><PERSON>", "filters": "Filter", "hide_filters": "Sembunyikan filter", "show_filters": "<PERSON><PERSON><PERSON><PERSON> filter", "apply": "Terapkan", "reset_filter": "Reset filter", "all": "<PERSON><PERSON><PERSON>", "in_progress": "Sedang berl<PERSON>g", "failed": "Gaga<PERSON>", "partial": "Sebagian", "canceled": "Di<PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "refill": "<PERSON><PERSON>", "moderation": "<PERSON><PERSON><PERSON>", "serial_number": "No.", "remaining": "<PERSON><PERSON><PERSON>", "notes": "Catatan", "reorder": "<PERSON><PERSON>", "warranty": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "started": "<PERSON><PERSON><PERSON>", "more_actions": "<PERSON><PERSON><PERSON> la<PERSON>", "remaining_quantity": "<PERSON><PERSON><PERSON>", "copy_id": "Salin ID", "copy_ids": "Salin ID", "copy_details": "Salin detail", "bulk_refill": "<PERSON><PERSON> ulang massal", "charge": "Biaya", "created": "Dibuat", "no_orders": "Tidak ada pesanan di<PERSON>n", "no_orders_desc": "Anda belum membuat pesanan apapun", "no_orders_found": "Tidak ada pesanan di<PERSON>n", "category": "<PERSON><PERSON><PERSON>", "service": "<PERSON><PERSON><PERSON>", "date": "Tanggal", "showing": "Menampilkan", "of": "dari", "entries": "entri", "actions": "<PERSON><PERSON><PERSON>", "link": "Tautan", "status": "Status", "amount": "<PERSON><PERSON><PERSON>", "quantity": "Kuantitas", "order": "<PERSON><PERSON><PERSON>"}, "nav": {"services": "<PERSON><PERSON><PERSON>", "new_order": "<PERSON><PERSON><PERSON> baru", "mass_order": "<PERSON><PERSON><PERSON> massal", "orders": "<PERSON><PERSON><PERSON>", "dripfeed": "Dripfeed", "warranty": "<PERSON><PERSON><PERSON>", "service_list": "<PERSON><PERSON><PERSON>", "currency": "<PERSON>", "cash_flow": "<PERSON><PERSON> kas", "top_up": "<PERSON><PERSON>ldo", "others": "<PERSON><PERSON><PERSON>", "support": "Dukungan", "settings": "<PERSON><PERSON><PERSON><PERSON>", "affiliates": "<PERSON><PERSON><PERSON><PERSON>", "update": "Pembaruan", "earn_money": "<PERSON><PERSON><PERSON>", "create_website": "<PERSON> anak", "api": "API", "extra_pages": "<PERSON><PERSON> tambahan", "login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON>"}, "auth": {"main_provider": "<PERSON><PERSON><PERSON> SMM #1", "cheapest_panel": "Panel SMM termurah untuk reseller", "intro_text": "Sebagai keluarga SmmProvider, kami adalah penyedia langsung lebih dari 50% layanan kami. Kami menyediakan layanan panel SMM termurah dengan dukungan terbaik. Bergabunglah dengan keluarga kami dan berkembanglah bersama!", "affordable_pricing": "Harga tetap terjangkau!", "lowest_reseller": "Harga reseller terendah terjamin", "multiple_payment": "Metode pembayaran beragam", "high_quality": "Layanan berkualitas tinggi", "support_24_7": "Dukungan 24/7", "orders_per_day": "Lebih dari 100.000 pesanan per hari", "in_seconds": "<PERSON><PERSON> hitungan detik..", "completed_orders": "<PERSON><PERSON><PERSON>", "order_every": "Pesanan diproses setiap", "price_starts": "Harga mulai dari", "our_services": "<PERSON><PERSON><PERSON> kami", "services_count": "<PERSON><PERSON><PERSON>", "why_choose_us": "Mengapa memilih kami?", "direct_provider": "<PERSON><PERSON><PERSON> langsung", "direct_provider_text": "<PERSON><PERSON> ad<PERSON>h penyedia langsung beberapa layanan SMM; kami menggunakan database sendiri, memberikan kualitas layanan tertinggi. Kami mere<PERSON>ikan menggunakan layanan ini karena mendukung Anda secara langsung.", "payment_options": "Opsi pembayaran beragam", "payment_options_text": "<PERSON><PERSON> berbagai metode pembayaran untuk pelanggan individu, termasuk kartu kredit, transfer bank, Google Pay, Payeer, Perfect Money, WebMoney dan cryptocurrency seperti Bitcoin, Litecoin dan USDT.", "affordable_prices": "<PERSON><PERSON> ter<PERSON><PERSON><PERSON>", "affordable_prices_text": "Kami menyediakan layanan media sosial berkualitas tinggi dengan harga terjangkau dalam industri yang kompetitif dan menuntut. Harga kami mulai dari hanya $0.0001/1000, menjadikan kami penyedia panel SMM termurah di pasar.", "api_integration": "Integrasi API", "api_integration_text": "<PERSON><PERSON> men<PERSON> integrasi API untuk semua layanan SMM kami, memungkinkan Anda mengotomatisasi pesanan dan mengintegrasikan fitur panel kami ke website atau bot Anda secara gratis.", "instant_delivery": "Pengiriman instan", "instant_delivery_text": "Jika Anda mencari layanan media sosial tercepat, SmmProvider adalah pilihan sempurna. Pesanan Anda dimulai segera dan akan dikirim secepat mungkin sesuai kuantitas.", "support_24_7_title": "Dukungan 24/7", "support_24_7_text": "Panel SMM kami memiliki tim dukungan pelanggan 24/7 yang siap membantu Anda dengan semua pertanyaan. Kami tidak pernah mengabaikan atau melewatkan pesan Anda sampai semuanya terselesaikan!", "faq": "<PERSON><PERSON><PERSON> yang sering diajukan", "faq_what_is_smm": "Apa itu panel SMM?", "faq_what_is_smm_answer": "Panel SMM (Social Media Marketing) adalah platform yang memungkinkan Anda membeli layanan media sosial seperti like, follower, views dan banyak layanan lainnya untuk berbagai platform media sosial.", "faq_what_is_smm_service": "Apa itu layanan pemasaran media sosial?", "faq_what_is_smm_service_answer": "Layanan pemasaran media sosial mencakup layanan yang membantu meningkatkan kehadiran dan keterlibatan di platform media sosial, seperti meningkatkan follower, like, views dan komentar.", "faq_best_smm_vietnam": "Apa panel SMM terbaik dan termurah di <PERSON>?", "faq_best_smm_vietnam_answer": "Layanan SMM kami menyed<PERSON>kan panel SMM terbaik dan termurah di Vietnam dengan harga kompetitif dan layanan berkualitas tinggi.", "faq_best_provider": "Siapa yang menyed<PERSON>kan layanan SMM terbaik?", "faq_best_provider_answer": "Dengan pengalaman bertahun-tahun dan komitmen ter<PERSON><PERSON> kualitas, kami bangga menjadi penyedia layanan SMM terbaik dengan dukungan 24/7 dan harga kompetitif.", "faq_cheapest_panel": "Apa panel SMM termurah?", "faq_cheapest_panel_answer": "Panel SMM termurah adalah platform yang menyediakan layanan media sosial dengan harga terendah di pasar sambil tetap memastikan kualitas. Layanan kami mulai dari hanya $0.0001/1000.", "faq_payment_methods": "Apa saja metode pembayaran untuk panel SMM?", "faq_payment_methods_answer": "<PERSON><PERSON>ma berbagai metode pembayaran seperti kartu kredit (Visa), Paytm, Perfect Money, Payeer, Binance Pay dan berbagai cryptocurrency lainnya.", "payment_methods": "<PERSON><PERSON>", "google_authentication_setting": "Pengaturan autentikasi Google", "get_google_authenticator": "Dapatkan Google Authenticator di ponsel Anda", "get_on_android": "Dapatkan di Android", "get_on_ios": "Dapatkan di iOS", "scan_qr_code_instruction": "Pindai kode QR. Ini akan men<PERSON> kode 6 digit yang perlu Anda masukkan di bawah.", "manual_entry_instruction": "Ji<PERSON> Anda mengalami kesulitan menggunakan kode QR, pilih entri manual di aplikasi dan masukkan username dan kode:", "enter_authentication_code": "Ma<PERSON>kka<PERSON> kode autentikasi", "code_required": "<PERSON><PERSON>", "code_must_be_6_digits": "<PERSON>de harus 6 digit", "enable": "Aktifkan", "disable": "Nonaktifkan", "disable_two_factor_auth": "Nonaktifkan autentikasi dua faktor", "disable_two_factor_auth_confirmation": "<PERSON><PERSON><PERSON> masukkan kata sandi Anda untuk menonaktifkan autentikasi dua faktor.", "disable_two_factor_auth_warning": "Peringatan: Menonaktifkan autentikasi dua faktor akan membuat akun Anda kurang aman. Siapa pun yang memiliki kata sandi Anda akan dapat mengakses akun Anda.", "register": "<PERSON><PERSON><PERSON>", "services": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON>", "enter_username": "<PERSON><PERSON><PERSON><PERSON> nama pengguna", "username_required": "<PERSON>a pengguna diperlukan", "username_invalid": "Nama pengguna tidak valid", "password": "<PERSON>a sandi", "forgot_password": "Lupa kata sandi?", "enter_password": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "password_required": "<PERSON>a sandi <PERSON>an", "remember_me": "<PERSON>gat saya", "login": "<PERSON><PERSON><PERSON>", "logging_in": "Masuk...", "forgot_password_description": "<PERSON><PERSON><PERSON><PERSON> email Anda dan kami akan mengirimkan tautan untuk mereset kata sandi Anda.", "email": "Email", "reset_email_sent_to": "Instruksi reset kata sandi telah dikirim ke", "resend_email": "<PERSON><PERSON> email", "send_reset_link": "<PERSON><PERSON> reset", "sending": "Mengirim...", "email_sent": "<PERSON><PERSON> te<PERSON>", "back_to_login": "<PERSON><PERSON><PERSON> ke masuk", "reset_password": "Reset kata sandi", "reset_password_description": "<PERSON><PERSON>kkan kata sandi baru Anda di bawah ini.", "new_password": "Kata sandi baru", "confirm_password": "Konfirmasi kata sandi", "enter_new_password": "<PERSON><PERSON>kkan kata sandi baru", "confirm_new_password": "Konfirmasi kata sandi baru", "password_min_length": "<PERSON><PERSON> sandi harus minimal 6 karakter", "confirm_password_required": "Kon<PERSON>rma<PERSON> kata sandi dip<PERSON>an", "passwords_do_not_match": "Kata sandi tidak cocok", "password_reset_success": "Reset kata sandi ber<PERSON>il", "password_reset_success_description": "Kata sandi Anda telah berhasil direset. Anda sekarang dapat masuk dengan kata sandi baru Anda.", "go_to_login": "<PERSON> halaman masuk", "resetting_password": "Mereset kata sandi..."}, "header": {"home": "Be<PERSON><PERSON>", "services": "<PERSON><PERSON><PERSON>", "pricing": "<PERSON><PERSON>", "grocery": "Bel<PERSON><PERSON>", "resources": "<PERSON><PERSON> <PERSON>", "blog": "Blog", "contact": "Kontak", "login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON>"}, "footer": {"about_us": "<PERSON>tang kami", "about_text": "SmmProvider adalah penyedia layanan SMM terkemuka, menawarkan layanan media sosial berkualitas tinggi dengan harga terjangkau.", "quick_links": "Tautan cepat", "home": "Be<PERSON><PERSON>", "services": "<PERSON><PERSON><PERSON>", "pricing": "<PERSON><PERSON>", "blog": "Blog", "contact": "Kontak", "support": "Dukungan", "faq": "FAQ", "terms": "<PERSON><PERSON><PERSON>", "privacy": "<PERSON><PERSON><PERSON><PERSON>", "contact_us": "<PERSON><PERSON><PERSON><PERSON> kami", "address": "<PERSON><PERSON><PERSON>", "address_text": "123 <PERSON><PERSON>, <PERSON><PERSON><PERSON> 1, <PERSON>", "phone": "Telepon", "email": "Email", "copyright": "© 2025 SmmProvider. Semua hak dilindungi.", "popular_services": "Layanan populer", "help_center": "<PERSON><PERSON><PERSON> ban<PERSON>an"}, "api_doc": {"title": "Dokumentasi API", "service": "<PERSON><PERSON><PERSON>", "api_url": "URL API", "api_key": "Kunci API", "https_method": "Metode HTTPS", "content_type": "<PERSON><PERSON>", "responsive": "Respons", "parameter": "Parameter", "description": "<PERSON><PERSON><PERSON><PERSON>", "example_responsive": "<PERSON><PERSON><PERSON> respons", "mass_order": "<PERSON><PERSON><PERSON> massal", "orders": "<PERSON><PERSON><PERSON>", "warranty": "<PERSON><PERSON><PERSON>", "service_list": "<PERSON><PERSON><PERSON>n", "synchronize": "Sink<PERSON><PERSON><PERSON>", "top_up": "<PERSON><PERSON>ldo", "support": "Dukungan", "questions": "<PERSON><PERSON><PERSON>", "update": "Pembaruan", "earn_money": "<PERSON><PERSON><PERSON>", "create_website": "Buat website"}, "services": {"refill": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "search_placeholder": "<PERSON>i layanan...", "search_by_name_or_id": "<PERSON>i berda<PERSON>kan nama atau ID", "price": "<PERSON><PERSON>", "min_order": "<PERSON><PERSON><PERSON> min", "max_order": "<PERSON><PERSON><PERSON> maks", "avg_time": "<PERSON><PERSON><PERSON> rata-rata", "refill_available": "<PERSON><PERSON> ulang tersedia", "cancel_available": "Pembatalan tersedia", "order_now": "<PERSON><PERSON>", "no_services": "Tidak ada layanan di<PERSON>n", "no_services_desc": "Tidak ada layanan tersedia untuk kriteria yang dipilih", "no_service_selected": "Tidak ada layanan dipilih", "select_service_to_view_info": "<PERSON><PERSON><PERSON> layanan untuk melihat detail", "no_service_found": "Tidak ada layanan yang cocok ditemukan", "favorite_services": "<PERSON><PERSON><PERSON> favorit", "no_favorite_services": "Tidak ada layanan favorit ditemukan", "no_services_available": "Tidak ada layanan tersedia", "select_platform_first": "<PERSON>lakan pilih platform terlebih dahulu", "information": "Informasi layanan", "warranty": "<PERSON><PERSON><PERSON>", "example_url": "Contoh URL", "min_value": "Minimum", "max_value": "<PERSON><PERSON><PERSON><PERSON>", "exchange_rate": "<PERSON><PERSON> tuka<PERSON>", "average_time": "<PERSON><PERSON><PERSON> rata-rata", "select_platform": "Pilih platform", "all_networks": "<PERSON><PERSON><PERSON>", "facebook": "Facebook", "instagram": "Instagram", "youtube": "YouTube", "auto_subscription": "<PERSON><PERSON><PERSON> otomatis", "classification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order_limit": "<PERSON><PERSON> p<PERSON>", "charge_fee": "<PERSON><PERSON><PERSON>", "start_time": "<PERSON><PERSON><PERSON> mulai", "immediately": "<PERSON><PERSON><PERSON>", "guarantee": "Jaminan", "refund_period": "Periode pengembalian dana", "speed": "Kecepatan", "growth_rate": "<PERSON><PERSON><PERSON>", "processing_time": "<PERSON><PERSON><PERSON>"}, "child_panel": {"create_child_panel": "Buat panel anak", "my_panels": "<PERSON><PERSON> <PERSON><PERSON> panel", "no_panels": "Anda belum memiliki panel", "domain": "Domain", "status": "Status", "created_at": "Dibuat pada", "actions": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>", "status_active": "Aktif", "status_waiting": "<PERSON><PERSON><PERSON>", "status_suspended": "<PERSON><PERSON><PERSON>", "status_new": "<PERSON><PERSON>", "status_unknown": "Tidak diketahui", "domain_name": "Nama domain", "exchange_rate": "<PERSON><PERSON> tuka<PERSON>", "create_website": "Buat website", "admin_name": "<PERSON><PERSON> admin", "admin_password": "Kata sandi admin", "confirm_admin_password": "Konfirmasi kata sandi admin", "retail_price": "<PERSON><PERSON> eceran", "payment": "Pembayaran", "domain_setup_guide": "Jika Anda memiliki domain, Anda hanya perlu mengubah name server...", "search_orders": "<PERSON><PERSON> pesanan", "go_button": "<PERSON><PERSON>", "copy_id": "Salin ID", "refill": "<PERSON><PERSON>", "title": "<PERSON> anak", "description": "Buat website Anda sendiri dengan Panel anak", "create_now": "<PERSON><PERSON><PERSON>", "features": "<PERSON><PERSON>", "feature_1": "Buat website Anda sendiri dengan domain Anda", "feature_2": "Kustomisasi antarmuka dan logo", "feature_3": "<PERSON><PERSON><PERSON> pesanan dan pelanggan", "feature_4": "Integrasi API", "feature_5": "Dukungan teknis 24/7", "feature_6": "<PERSON><PERSON><PERSON> tinggi", "domain_setup_info": "Jika Anda memiliki domain, Anda hanya perlu mengubah nameserver untuk domain Anda dan arahkan ke:", "faq_title": "<PERSON><PERSON><PERSON> yang <PERSON>", "faq_1_question": "Apa itu panel SMM?", "faq_1_answer": "Panel SMM adalah platform online di mana Anda dapat membeli berbagai layanan pemasaran media sosial untuk meningkatkan kehadiran online klien Anda.", "faq_2_question": "Apa itu panel anak?", "faq_2_answer": "Panel anak adalah versi panel SMM yang dapat Anda miliki dan kelola. Ini memungkinkan Anda untuk menjual kembali layanan SMM di bawah merek Anda sendiri.", "faq_3_question": "Layanan SMM apa saja yang tersedia?", "faq_3_answer": "<PERSON><PERSON>n berbagai layanan SMM seperti follower, like, views, komentar di platform media sosial populer seperti Facebook, Instagram, YouTube, TikTok, Twitter dan banyak platform lainnya.", "faq_4_question": "Bagaimana cara mengelola panel SMM?", "faq_4_answer": "Anda dapat mengelola panel SMM melalui dashboard admin, di mana Anda dapat menambahkan layanan, mengel<PERSON> pesanan, melacak pembayaran dan mengelola pengguna.", "faq_5_question": "Bagaimana cara mencari pelanggan?", "faq_5_answer": "Anda dapat mencari pelanggan melalui pemasaran online, media sosial, pemasaran afiliasi, SEO dan iklan berbayar. Membangun hubungan dengan reseller dan influencer juga dapat membantu Anda memperluas basis pelanggan.", "faq_6_question": "Bagaimana cara mencari pema<PERSON>k?", "faq_6_answer": "Anda dapat mencari pemasok layanan SMM melalui forum online, grup Facebook dan marketplace SMM. Pastikan untuk meneliti secara menyeluruh dan memeriksa ulasan sebelum memilih pemasok."}, "ticket": {"title": "Dukungan", "support": "Dukungan", "create_ticket": "Create support ticket", "search": "<PERSON><PERSON>", "search_placeholder": "Cari berdasarkan ID atau deskripsi...", "select_all": "<PERSON><PERSON><PERSON> se<PERSON>a", "id": "ID", "subject": "Subject", "status": "Status", "last_update": "Last update", "customer": "Customer", "ticket_details": "TICKET DETAILS", "ticket_id": "TICKET ID", "request": "REQUEST", "priority": "PRIORITY", "department": "DEPARTMENT", "response_time": "RESPONSE TIME", "reply_now": "REPLY NOW", "type_message": "Type your message here...", "auto_reset": "Auto reset after", "new_ticket": "New ticket", "order": "<PERSON><PERSON><PERSON>", "payment": "Payment", "point": "Point", "other": "<PERSON><PERSON><PERSON>", "orders_id": "ORDER ID", "refill": "Refill", "cancel": "<PERSON><PERSON>", "speed_up": "Speed up", "message": "MESSAGE", "submit_now": "SUBMIT NOW", "created_at": "Created at", "updated_at": "Updated at", "note": "Note", "user": "User", "no_tickets": "No tickets found", "error": "<PERSON><PERSON><PERSON>", "api_timeout": "API timeout when updating messages", "api_timeout_load": "API timeout when loading ticket data", "sending": "Sending", "closed": "Ticket closed", "closed_message": "This ticket has been closed. You cannot send any more messages.", "pending_message": "Your ticket is pending. The support team will review it soon.", "accept_message": "Your ticket has been accepted. Our team is working on it.", "solved_message": "Your ticket has been resolved. Please let us know if you need further assistance."}, "add_fund": {"title": "Add funds", "payment_note": "Note: Payment may take 5-15 minutes to process. Contact support via Telegram for example", "member_rank": "Member rank", "total_deposit": "Total deposit", "total_bonus": "Total bonus", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "payment_method": "Select your preferred payment method", "scan_qr": "Scan QR code to pay", "transfer_to": "Transfer to", "to": "to", "account_number": "Account number", "account_name": "Account name", "transfer_content": "Transfer content", "crypto": "Cryptocurrency", "register_marketplace": "Register to connect with marketplace website", "amount": "<PERSON><PERSON><PERSON>", "custom_amount": "Custom amount", "pay": "Pay", "transaction_history": "Transaction history", "transaction_id": "Transaction ID", "date": "Tanggal", "type": "Type", "method": "Method", "transaction_amount": "<PERSON><PERSON><PERSON>", "select_payment_method": "Select payment method", "search_payment_methods": "Search payment methods...", "no_payment_methods_found": "No payment methods found", "bonus_conditions": "Bonus conditions", "min_amount": "Minimum amount", "bonus_amount": "Bonus amount", "currency": "<PERSON>", "from_date": "From date", "to_date": "To date", "date_range": "Date range", "no_transactions": "No transactions found", "bank": "Bank", "account": "Account number", "name": "<PERSON><PERSON>", "content": "Content", "copy_account": "Copy account number", "copy_content": "Copy content", "transfer_amount": "Transfer amount", "bank_info": "Bank information", "bank_name": "Bank name", "transfer_content_note": "Please enter this content exactly when making the transfer", "amount_limits": "Amount limits"}, "cash_flow": {"title": "Cash flow", "subtitle": "Track your transaction history", "total_transactions": "Total transactions", "cash_flow_diary": "Cash flow diary", "all_transactions": "All transactions", "transaction_type": "Transaction type", "order_id": "Order ID", "no_transactions": "No transactions found", "id": "No.", "link": "Tautan", "total_amount": "Total amount", "cash_flow": "Cash flow", "description": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Reset", "no_transactions_found": "No transactions found", "no_transactions_found_desc": "You don't have any transactions yet", "date_range": "Date range"}, "notifications": {"title": "Notif<PERSON><PERSON>", "dismiss": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "mark_as_read": "<PERSON> as read", "new_announcement": "New announcement", "no_more_display": "Viewed", "unread": "unread", "unread_filter": "Unread", "all_filter": "<PERSON><PERSON><PERSON>", "loading": "Loading...", "no_notifications": "No notifications", "no_unread_notifications": "No unread notifications", "view_all": "View all notifications"}, "update": {"title": "Pembaruan", "search": "<PERSON><PERSON>", "service": "<PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON><PERSON>", "status": "Status", "all": "<PERSON><PERSON><PERSON>", "price_increase": "Price increase", "price_decrease": "Price decrease", "new": "<PERSON><PERSON>", "on": "On", "off": "Off", "showing": "Showing", "of": "of", "entries": "entries", "show": "<PERSON><PERSON><PERSON><PERSON>", "no_data": "No update logs found", "refresh": "Segarkan", "from": "from", "to": "to"}, "affiliates": {"referral_commission": "Referral commission", "affiliate_program": "Affiliate program", "affiliates": "Affiliates", "payment_history": "Payment history", "referral_link": "Referral link:", "copy_your_link": "COPY YOUR LINK", "copied": "COPIED!", "commission_rate": "Commission rate", "affiliate_system": "Affiliate system", "loyalty_program": "Loyalty program and percentage rates", "enable_on_panel": "Enable on panel", "set_percentage": "Set referral income percentage", "save": "Simpan", "total_referrals": "Total referrals", "commission_earnings": "Commission earnings", "successful_referrals": "Successful referrals", "no_referrals": "No referrals found", "share_link": "Share referral link to start earning commissions", "payment_history_empty": "Your affiliate payment history will appear here", "program_description": "Earn money by referring new users to our platform. You will receive commission for each purchase they make.", "share_link_info": "Share this link with friends, on social media or on your website to earn commissions.", "per_referral": "per referral", "active_users": "active users", "total_earned": "total earned", "current_balance": "current balance", "commission_info": "Commission is calculated based on referred user's deposit transactions", "invite_friends": "Invite friends", "payment_history_description": "Track all your affiliate earnings and payment transactions in one place.", "no_transactions": "No transactions yet", "request_payment": "Request payment", "view_requirements": "View requirements", "enable_affiliate": "Enable affiliate system", "percentage_affiliate": "Affiliate percentage", "default_rate": "Default rate", "custom_rate": "Custom rate", "pending_commissions": "Pending commissions", "paid_commissions": "Paid commissions", "commission_status": "Commission status", "pending": "<PERSON><PERSON><PERSON>", "paid": "Paid", "referral_code": "Referral code", "referred_user": "Referred user", "transaction_note": "Transaction note", "commission_amount": "Commission amount", "user_referrals": "User referrals", "referral_stats": "Referral stats", "my_referrals": "My referrals", "my_commissions": "My commissions", "affiliate_link": "Affiliate link", "effective_rate": "Effective rate", "using_custom_rate": "Using custom rate", "using_default_rate": "Using tenant default rate", "transaction": "Transaction", "transaction_id": "Transaction ID", "transaction_details": "Transaction details", "transaction_history": "Transaction history", "transaction_amount": "Transaction amount", "transaction_date": "Transaction date", "transaction_status": "Transaction status", "transaction_type": "Transaction type", "transaction_reference": "Transaction reference", "view_transaction": "View transaction", "transaction_summary": "Transaction summary", "recent_transactions": "Recent transactions", "all_time": "All time", "awaiting_payment": "Awaiting payment", "total_received": "Total received", "commission_payments": "Commission payments", "requirements": "Requirements", "pending_payments": "Pending payments", "total_commissions": "Total commissions"}, "landing": {"header": {"services": "<PERSON><PERSON><PERSON>", "features": "Features", "pricing": "Pricing", "login": "<PERSON><PERSON><PERSON>", "signup": "<PERSON><PERSON><PERSON>"}, "hero": {"title": "The most convenient SMM provider", "subtitle": "We will make you famous with high-quality social media services", "signup_button": "Sign up now", "dashboard_button": "Dashboard"}, "stats": {"happy_customers": "Thousands of happy customers", "completed_orders": "Millions of completed orders", "quality_services": "High-quality services"}, "services": {"title": "Huge service inventory", "subtitle": "On all popular social networks", "view_all": "View all services", "followers": "Followers", "likes": "<PERSON>s", "views": "Views"}, "features": {"title": "We have everything you need", "subtitle": "High-quality services, convenient panel, great support and our API", "stats": "Real-time statistics", "stats_desc": "Track orders and account activity with detailed analytics and reports.", "payments": "Secure payments", "payments_desc": "Multiple payment methods with secure processing and transaction history.", "api": "API integration", "api_desc": "Easily integrate our services with your application using our powerful API.", "support": "24/7 Support", "support_desc": "Dedicated support team always ready to help you with any questions."}, "quality": {"title": "Great quality and affordable prices", "amazing_quality": "Amazing quality", "amazing_quality_desc": "We monitor our services at all times. If quality doesn't meet expectations, you'll get a full refund.", "affordable_prices": "Affordable prices", "affordable_prices_desc": "Our panel only works with direct providers, you get the cheapest prices from over 400 items.", "get_started": "Get started", "price_prefix": "$"}, "footer": {"slogan": "The most convenient SMM provider", "copyright": "© 2023 NEWPANEL. All rights reserved.", "terms": "Terms of service", "privacy": "Privacy policy", "contact": "Kontak"}}, "profile": {"title": "Profil", "information": "Informasi", "email": "Email", "phone_number": "Phone number", "current_password": "Kata sandi saat ini", "new_password": "Kata sandi baru", "confirm_password": "Konfirmasi kata sandi", "update": "Pembaruan", "updating": "Updating...", "update_password": "Update password", "updating_password": "Updating password...", "change_password": "Ubah kata sandi", "verify": "Verify", "verifying": "Verifying...", "already_verified": "Already verified", "email_required": "Email is required", "email_invalid": "<PERSON><PERSON> is invalid", "phone_required": "Phone number is required", "current_password_required": "Current password is required", "new_password_required": "New password is required", "confirm_password_required": "Confirm password is required", "password_min_length": "Password must be at least 8 characters", "password_no_space": "Password cannot contain spaces", "password_invalid_characters": "Password contains invalid characters", "passwords_not_match": "Passwords do not match", "password_updated": "Password updated successfully", "password_error": "Unable to update password", "profile_updated": "Profile updated successfully", "profile_error": "Unable to update profile", "account": "<PERSON><PERSON><PERSON>", "online": "Online", "balance": "<PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON>", "status": "Status", "successful": "Successful", "page": "Page", "of": "of", "linked_accounts": "Linked accounts", "link_telegram_to_receive_notifications": "Link your Telegram account to receive important notifications.", "link_telegram": "Link with Telegram", "telegram_username": "Telegram username", "open_telegram_bot": "Open Telegram Bot", "telegram_guide": "Connection guide", "security": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON>", "login_history_tab": "Login history", "two_factor_auth": "Two-factor authentication", "language": "Bahasa", "currency": "<PERSON>", "appearance": "Appearance", "timezone": "Zona waktu", "login_history": "Login history", "loading_history": "Loading history...", "no_login_history": "No login history", "time": "<PERSON><PERSON><PERSON>", "ip": "IP address", "device": "<PERSON><PERSON>", "login_records": "login records", "two_factor_auth_description": "Add an extra layer of security to your account by requiring a verification code in addition to your password.", "enable": "Aktifkan", "telegram_guide_popup": {"title": "🔧 Guide to connect account with Telegram bot", "step1_title": "Step 1: Get API Key", "step1_content": ["Go to /dashboard/apidoc page.", "Find the API Key section.", "Click on the icon (2 rotating arrows) to create a new API Key.", "Copy the newly created API Key for use in the next step."], "step2_title": "Step 2: Open Telegram bot", "step2_content": ["Go to /dashboard/settings page, select Account tab.", "Click \"Link with Telegram\" button.", "This action will automatically open Telegram app and take you to the system bot."], "step3_title": "Step 3: Authenticate with bot", "step3_content": ["In the chat with Telegram bot, send a message with the syntax:", "/auth <APIKEY>", "Where <APIKEY> is the code you copied in step 1.", "Example: /auth 1234abcd5678efgh"], "complete_title": "✅ Complete", "complete_content": ["If the API Key is valid, the bot will report successful authentication.", "You have successfully linked your account and can start using features via Telegram bot."]}, "email_verification": {"title": "Email verification", "sent_title": "Verification email sent!", "sent_message": "We have sent a verification email to:", "instructions": "Please follow these steps to complete verification:", "step1": "Check your inbox", "step2": "Find the email from us with subject 'Verify email address'", "step3": "Click the verification link in the email", "note": "If you don't see the email, please check your spam or junk folder.", "resend": "Resend email", "verifying": "Verifying email...", "please_wait": "Please wait while we verify your email.", "success_title": "Email verified successfully!", "success_message": "Your account has been verified. You can login now.", "go_to_login": "Go to login", "error_title": "Email verification failed", "error_message": "There was an error verifying your email. Please try again later.", "go_to_home": "Go to home"}}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "language": "Bahasa", "currency": "<PERSON>", "appearance": "Appearance", "timezone": "Zona waktu", "login_history": "Login history", "login_history_tab": "Login history", "loading_history": "Loading history...", "no_login_history": "No login history", "time": "<PERSON><PERSON><PERSON>", "ip": "IP address", "device": "<PERSON><PERSON>", "login_records": "login records", "security": "<PERSON><PERSON><PERSON>", "two_factor_auth": "Two-factor authentication", "two_factor_auth_description": "Add an extra layer of security to your account by requiring a verification code in addition to your password.", "enable": "Aktifkan", "disable": "Nonaktifkan", "enabled": "Diaktifkan", "disabled": "Dinonaktifkan", "api_key": "API Key", "api_key_description": "Use this key to access our API services.", "generate": "Generate", "regenerate": "Regenerate", "copy": "<PERSON><PERSON>", "copied": "Copied!", "never_share": "Never share your API key with anyone.", "last_generated": "Last generated", "interface_style": "Interface style", "choose_interface_description": "Choose your preferred interface style", "interface_saved_automatically": "Interface options are saved automatically"}, "dripfeed": {"title": "Dripfeed management", "service": "<PERSON><PERSON><PERSON>", "link": "Tautan", "quantity": "<PERSON><PERSON><PERSON>", "schedule": "Schedule", "loop_settings": "Loop settings", "status": "Status", "reason": "Reason", "orders": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "no_dripfeeds": "No dripfeeds found", "no_dripfeeds_description": "You haven't created any scheduled orders yet.", "create_first": "Create your first order", "search_placeholder": "Search by link or ID...", "stop": "<PERSON><PERSON><PERSON><PERSON>"}, "home": {"welcome_to": "Welcome to", "completed_orders": "Completed orders", "account_balance": "Account balance", "account_spent": "Account spent"}, "transaction_types": {"order": "<PERSON><PERSON><PERSON>", "spent": "Spent", "bonus": "Bonus", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "refund": "Refund", "all": "All transaction types"}, "favorites": {"title": "Favorites", "favorite_services": "Favorite services", "no_favorites": "No favorite services found", "min_value": "Minimum value", "max_value": "Maximum value", "exchange_rate": "Exchange rate", "average_time": "Average time"}, "filter": {"all_platforms": "All platforms", "all_categories": "All categories", "all_services": "All services", "button": "Filter", "select_category": "Select category", "select_service": "Select service", "select_platform": "Select platform"}, "time": {"hours": "hours", "minutes": "minutes", "seconds": "seconds"}, "avatar": {"agent": "Agent", "account_info": "Account info", "change_password": "Ubah kata sandi", "balance": "<PERSON><PERSON>", "total_orders": "Total orders", "logout": "<PERSON><PERSON><PERSON>"}, "error": {"not_found_title": "Page not found", "not_found_message": "The page you are looking for does not exist or has been moved.", "unauthorized_title": "Unauthorized access", "unauthorized_message": "You need to login to access this page.", "forbidden_title": "Access forbidden", "forbidden_message": "You do not have permission to access this page.", "server_error_title": "Server error", "server_error_message": "There is a problem with the server. Please try again later.", "go_back": "Go back", "go_home": "Go home", "login": "<PERSON><PERSON><PERSON>", "refresh": "Refresh page"}, "simple_theme": {"auth": {"login": "<PERSON><PERSON><PERSON>", "welcome_back": "Welcome back", "username": "<PERSON><PERSON>", "enter_username": "Enter username", "password": "<PERSON>a sandi", "enter_password": "Enter password", "username_required": "Username is required", "username_invalid": "Invalid username format", "password_required": "Password is required", "remember_me": "<PERSON>gat saya", "forgot_password": "Forgot password?", "logging_in": "Logging in...", "no_account": "Don't have an account?", "register_now": "Register now", "completed_orders": "Completed orders", "support": "Dukungan", "price_starts": "Prices start"}, "new_order": {"new_order": "<PERSON><PERSON><PERSON> baru", "schedule_and_loop": "Schedule & Loop", "schedule": "Schedule", "your_timezone": "Your timezone", "loop": "Loop", "auto_reorder_when_completed": "Auto reorder", "carefully_use_ensure_balance": "Use this feature carefully. Always ensure sufficient balance", "wait_completion": "when this order is COMPLETED", "loop_quantity": "Loop quantity", "loop_spacing": "Loop spacing", "minutes": "minutes"}, "orders": {"title": "<PERSON><PERSON><PERSON>", "search_placeholder": "Search orders...", "selected": "selected", "copy_ids": "Copy IDs", "copy_details": "Copy details", "bulk_refill": "Bulk refill", "id": "ID", "service": "<PERSON><PERSON><PERSON>", "link": "Tautan", "quantity": "<PERSON><PERSON><PERSON>", "charge": "Charge", "status": "Status", "created": "Dibuat", "no_orders": "No orders found", "no_orders_desc": "You haven't placed any orders yet"}, "services": {"title": "<PERSON><PERSON><PERSON>", "search_placeholder": "Search services...", "services": "services", "price": "<PERSON><PERSON>", "min_order": "Min order", "max_order": "Max order", "avg_time": "Avg time", "refill_available": "Refill available", "cancel_available": "Cancel available", "order_now": "Order now", "no_services": "No services found", "no_services_desc": "No services available for selected criteria", "information": "Service information", "warranty": "Warranty", "no_service_selected": "No service selected", "select_service_to_view_info": "Select a service to view details", "no_service_found": "No matching service found"}, "cash_flow": {"title": "Cash flow", "subtitle": "Track your transaction history", "total_transactions": "Total transactions"}, "platforms": {"select": "Select platform"}, "mass_order": {"bulk_orders": "Bulk orders", "bulk_description": "Create multiple orders efficiently with the bulk ordering system", "help_title": "Need help?", "lines": "Lines", "characters": "Characters", "detailed_results": "Detailed results", "result_successful": "Successful", "result_failed": "Gaga<PERSON>"}, "common": {"loading": "Memuat", "error": "<PERSON><PERSON><PERSON>", "redirecting": "Redirecting", "created": "Dibuat", "updated": "<PERSON><PERSON><PERSON><PERSON>", "filter": "Filter", "search": "<PERSON><PERSON>", "date_range": "Date range", "category": "<PERSON><PERSON><PERSON>", "service": "<PERSON><PERSON><PERSON>", "platform": "Platform", "reset": "Reset", "apply": "Terapkan", "actions": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "sending": "Mengirim...", "go_to_dashboard": "Ke Dashboard"}, "filter": {"select_category": "<PERSON><PERSON><PERSON> ka<PERSON>i", "select_service": "<PERSON><PERSON><PERSON>n", "select_platform": "Pilih platform"}}}