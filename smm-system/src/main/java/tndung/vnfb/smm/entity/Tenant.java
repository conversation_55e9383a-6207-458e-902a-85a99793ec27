package tndung.vnfb.smm.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Formula;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.entity.audit.AbstractCreatedAuditEntity;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a tenant in the multi-tenant system
 */
@Entity
@Table(name = "tenant")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Tenant extends AbstractCreatedAuditEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 2L;

    @Id
    @Column(length = 36)
    private String id;


    @Column(unique = true, nullable = false)
    private String domain;
//
//    @Column(name = "subdomain")
//    private String subdomain;


    @Column(columnDefinition = "Numberic(1)")
    @Enumerated(EnumType.ORDINAL)
    private TenantStatus status = TenantStatus.New;

    @Column(name = "api_url")
    private String apiUrl;

    @Column(name = "site_url")
    private String siteUrl;

    @Column(name = "main")
    private Boolean main = false;


    @Column(name = "contact_email")
    private String contactEmail;

    private Boolean isDeleted = false;




    @Column(name = "subscription_start_date")
    private ZonedDateTime subscriptionStartDate;

    @Column(name = "subscription_end_date")
    private ZonedDateTime subscriptionEndDate;



   // @Formula("EXTRACT(DAY FROM (subscription_end_date - CURRENT_TIMESTAMP))")
    private Integer daysUntilExpiration;

    @Column(name = "auto_renewal")
    private Boolean autoRenewal = false;

    @Column(name = "renewal_notification_sent")
    private Boolean renewalNotificationSent = false;

    @Column(name = "last_renewal_date")
    private ZonedDateTime lastRenewalDate;


    @Column(name = "grace_period_days")
    private Integer gracePeriodDays = 7;

    @Column(name = "default_language", columnDefinition = "VARCHAR(4)")
    private String defaultLanguage = "vi";

    @Column(name = "available_languages", columnDefinition = "VARCHAR(50)")
    private String availableLanguages = "vi,en";

    @Column(name = "available_currencies", columnDefinition = "VARCHAR(200)")
    private String availableCurrencies = "USD";

    @Column(name = "last_currency_sync")
    private ZonedDateTime lastCurrencySync;

    @Column(name = "enable_affiliate")
    private Boolean enableAffiliate = false;

    @Column(name = "percentage_affiliate", precision = 5, scale = 2)
    private BigDecimal percentageAffiliate = BigDecimal.ZERO;

    @Column(name = "enable_discount_system")
    private Boolean enableDiscountSystem = false;

    @Column(name = "design_settings", columnDefinition = "TEXT")
    private String designSettings;

    @Column(name = "decimal_places")
    private Integer decimalPlaces = 2;

    @Column(name = "connection_settings", columnDefinition = "TEXT")
    private String connectionSettings;

    @Column(name = "average_time_settings", columnDefinition = "TEXT")
    private String averageTimeSettings;

    // Sub-tenant hierarchy fields (Single level only: Parent -> Child)
    @Column(name = "parent_tenant_id", length = 36)
    private String parentTenantId;

    // Owner field - ID of the user who owns this tenant
    @Column(name = "owner_id")
    private Long ownerId;

    // JPA relationships for single-level hierarchy
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_tenant_id", insertable = false, updatable = false)
    private Tenant parentTenant;

    @OneToMany(mappedBy = "parentTenant", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Tenant> subTenants = new ArrayList<>();

    // JPA relationship for owner
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id", insertable = false, updatable = false)
    private GUser owner;

    // Helper methods
    public boolean isExpired() {
        return subscriptionEndDate != null && subscriptionEndDate.isBefore(ZonedDateTime.now());
    }

    public boolean isExpiringSoon(int days) {
        return subscriptionEndDate != null &&
                subscriptionEndDate.isBefore(ZonedDateTime.now().plusDays(days)) &&
                subscriptionEndDate.isAfter(ZonedDateTime.now());
    }

    public Integer getDaysUntilExpiration() {
        if( subscriptionEndDate == null) return null;
        return Math.toIntExact(ChronoUnit.DAYS.between(ZonedDateTime.now(), subscriptionEndDate));
    }

    // Sub-tenant helper methods (Single level hierarchy only)
    public boolean isParentTenant() {
        return parentTenantId == null;
    }

    public boolean isSubTenant() {
        return parentTenantId != null;
    }

    public void setAsSubTenant(String parentId) {
        this.parentTenantId = parentId;
    }

    public void setAsParentTenant() {
        this.parentTenantId = null;
    }

    public void setApiUrl() {
        this.apiUrl = String.format("https://%s/api/v1", this.domain);
    }

}