package tndung.vnfb.smm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Data
@Component
@ConfigurationProperties(prefix = "app.subscription")
public class SubscriptionProperties {

    /**
     * Cost for subscription renewal
     */
    private BigDecimal renewalCost = new BigDecimal("7.7");

    /**
     * Default renewal period in days
     */
    private int defaultRenewalDays = 30;

    /**
     * Grace period for suspended tenants in days
     */
    private int gracePeriodDays = 7;

    /**
     * Days ahead to check for expiring tenants
     */
    private int expirationNotificationDays = 7;
}