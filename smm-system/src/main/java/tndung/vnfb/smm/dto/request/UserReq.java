package tndung.vnfb.smm.dto.request;


import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import tndung.vnfb.smm.anotation.ValidPassword;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

@Data
@Builder
public class UserReq {

//    private String name;

    @NotBlank(message = "{validation.userName.NotBlank}")
    @Length(min = 5, max=30, message = "{validation.userName.Length}")
    private String userName;

    @Email(message = "{validation.email.Email}")
    @NotBlank(message = "{validation.email.NotBlank}")
    private String email;


    private String phone;


    @ValidPassword(message = "{validation.password.ValidPassword}")
    @NotBlank(message = "{validation.password.NotBlank}")
    private String password;


    private String referralCode;


}
