package tndung.vnfb.smm.dto.response;

import lombok.Data;
import lombok.Builder;
import tndung.vnfb.smm.constant.enums.TenantStatus;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * Response DTO for tenant hierarchy information
 */
@Data
@Builder
public class TenantHierarchyResponse {
    
    private String id;
    
    private String domain;
    
    private TenantStatus status;
    
    private String apiUrl;
    
    private String siteUrl;
    
    private String contactEmail;
    
    private Boolean main;
    
    private OffsetDateTime createdAt;
    
    // Hierarchy specific fields
    private String parentTenantId;


    private ZonedDateTime lastRenewalDate;

    private Integer daysUntilExpiration;

    private ZonedDateTime subscriptionStartDate;
    private ZonedDateTime subscriptionEndDate;

    
    // Nested sub-tenants for tree structure
    private List<TenantHierarchyResponse> subTenants;
    
    // Parent tenant info (for breadcrumb)
    private TenantHierarchyResponse parentTenant;
}
