package tndung.vnfb.smm.dto.request;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.TenantStatus;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Email;

/**
 * Request DTO for creating sub-tenant
 */
@Data
public class SubTenantCreateRequest {
    
    @NotBlank(message = "Parent tenant ID is required")
    private String parentTenantId;
    
    @NotBlank(message = "Domain is required")
    private String domain;
    
    private String apiUrl;
    
    private String siteUrl;
    
    @Email(message = "Invalid email format")
    private String contactEmail;
    
    private TenantStatus status = TenantStatus.Waiting;
    
    // Settings that can be inherited or overridden
    private String defaultLanguage;
    
    private String availableLanguages;
    
    private String availableCurrencies;
    
    private Boolean enableAffiliate;
    
    private Boolean enableDiscountSystem;
    
    private String designSettings;
    
    private Integer decimalPlaces;
    
    private String connectionSettings;
    
    private String averageTimeSettings;
}
