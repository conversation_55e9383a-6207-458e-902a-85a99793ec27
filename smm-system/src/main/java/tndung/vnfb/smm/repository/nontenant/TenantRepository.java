package tndung.vnfb.smm.repository.nontenant;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.entity.Tenant;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;


public interface TenantRepository extends JpaRepository<Tenant, String> {
    Optional<Tenant> findByDomain(String domain);

    Optional<Tenant> findByMainIsTrue();

    void deleteByDomain(String domain);

    /**
     * Find all tenants with a specific status
     *
     * @param status The status to filter by
     * @return List of tenants with the specified status
     */
    List<Tenant> findByStatus(TenantStatus status);





    // Find tenants expiring soon
    @Query("SELECT t FROM Tenant t WHERE t.status = :status " +
            "AND t.subscriptionEndDate BETWEEN :startDate AND :endDate " +
            "AND t.renewalNotificationSent = false")
    List<Tenant> findTenantsExpiringSoon(
            @Param("status") TenantStatus status,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    // Find expired tenants
    @Query("SELECT t FROM Tenant t WHERE t.status = tndung.vnfb.smm.constant.enums.TenantStatus.Active " +
            "AND t.subscriptionEndDate < CURRENT_TIMESTAMP")
    List<Tenant> findExpiredTenants();

    // Find tenants in grace period to suspend
    @Query("SELECT t FROM Tenant t WHERE t.status = tndung.vnfb.smm.constant.enums.TenantStatus.Expired " +
            "AND t.subscriptionEndDate < :gracePeriodEnd")
    List<Tenant> findTenantsToSuspend(@Param("gracePeriodEnd") LocalDateTime gracePeriodEnd);

    // Bulk update expired tenants
    @Modifying
    @Query("UPDATE Tenant t SET t.status = tndung.vnfb.smm.constant.enums.TenantStatus.Expired " +
            "WHERE t.status = tndung.vnfb.smm.constant.enums.TenantStatus.Active AND t.subscriptionEndDate < CURRENT_TIMESTAMP")
    int updateExpiredTenants();

    // Bulk update suspended tenants
    @Modifying
    @Query("UPDATE Tenant t SET t.status = tndung.vnfb.smm.constant.enums.TenantStatus.Suspended " +
            "WHERE t.status = tndung.vnfb.smm.constant.enums.TenantStatus.Expired " +
            "AND t.subscriptionEndDate < :gracePeriodEnd")
    int updateSuspendedTenants(@Param("gracePeriodEnd") LocalDateTime gracePeriodEnd);

    // Find by subscription status


    // Find active tenants
    @Query("SELECT t FROM Tenant t WHERE t.status = tndung.vnfb.smm.constant.enums.TenantStatus.Active AND t.isDeleted = false")
    List<Tenant> findActiveTenants();

    // Find expired tenants with auto-renewal enabled
    @Query("SELECT t FROM Tenant t WHERE t.subscriptionEndDate < CURRENT_TIMESTAMP " +
            "AND t.autoRenewal = true AND t.status != tndung.vnfb.smm.constant.enums.TenantStatus.Suspended")
    List<Tenant> findExpiredTenantsWithAutoRenewal();

    // Find tenants with main = false (panels)
    List<Tenant> findByMainFalseOrderByCreatedAt();

    // Find tenants with main = false and domain containing search term
    List<Tenant> findByMainFalseAndDomainContainingIgnoreCaseOrderByCreatedAt(String domain);

    // Sub-tenant hierarchy queries (Single level: Parent -> Child only)

    /**
     * Find all direct sub-tenants of a parent tenant
     */
    List<Tenant> findByParentTenantIdAndIsDeletedFalseOrderByCreatedAt(String parentTenantId);

    /**
     * Find all parent tenants (tenants that can have sub-tenants)
     */
    @Query("SELECT t FROM Tenant t WHERE t.parentTenantId IS NULL AND t.isDeleted = false ORDER BY t.createdAt")
    List<Tenant> findAllParentTenants();

    /**
     * Find all sub-tenants (child tenants)
     */
    @Query("SELECT t FROM Tenant t WHERE t.parentTenantId IS NOT NULL AND t.isDeleted = false ORDER BY t.createdAt")
    List<Tenant> findAllSubTenants();

    /**
     * Count direct sub-tenants of a parent
     */
    @Query("SELECT COUNT(t) FROM Tenant t WHERE t.parentTenantId = :parentTenantId AND t.isDeleted = false")
    Long countSubTenants(@Param("parentTenantId") String parentTenantId);

    /**
     * Check if tenant can create sub-tenants (only parent tenants can create sub-tenants)
     */
    @Query("SELECT CASE " +
           "WHEN t.parentTenantId IS NOT NULL THEN false " +
           "ELSE true END " +
           "FROM Tenant t WHERE t.id = :tenantId")
    Boolean canCreateSubTenant(@Param("tenantId") String tenantId);

    /**
     * Find parent tenant of a sub-tenant
     */
    @Query("SELECT p FROM Tenant p WHERE p.id = (SELECT t.parentTenantId FROM Tenant t WHERE t.id = :subTenantId)")
    Optional<Tenant> findParentTenant(@Param("subTenantId") String subTenantId);

    /**
     * Check if tenant is a sub-tenant
     */
    @Query("SELECT CASE WHEN t.parentTenantId IS NOT NULL THEN true ELSE false END FROM Tenant t WHERE t.id = :tenantId")
    Boolean isSubTenant(@Param("tenantId") String tenantId);

    /**
     * Find all tenants with their parent-child relationship
     */
    @Query("SELECT t FROM Tenant t LEFT JOIN FETCH t.parentTenant LEFT JOIN FETCH t.subTenants WHERE t.isDeleted = false ORDER BY  t.createdAt")
    List<Tenant> findAllWithHierarchy();

    /**
     * Find all tenants owned by a specific user
     */
    List<Tenant> findByOwnerIdAndIsDeletedFalse(Long ownerId);

    /**
     * Find tenants that can become parents (not sub-tenants)
     */
    @Query("SELECT t FROM Tenant t WHERE t.parentTenantId IS NULL AND t.isDeleted = false ORDER BY t.domain")
    List<Tenant> findPotentialParentTenants();

//    @Query("SELECT CASE " +
//            "WHEN :newParentId IS NULL THEN true " +
//            "WHEN EXISTS (SELECT 1 FROM Tenant t WHERE t.id = :newParentId AND t.parentTenantId IS NOT NULL) THEN false " +
//            "WHEN :tenantId = :newParentId THEN false " +
//            "ELSE true END")
//    Boolean canMoveToParent(@Param("tenantId") String tenantId, @Param("newParentId") String newParentId);
}
