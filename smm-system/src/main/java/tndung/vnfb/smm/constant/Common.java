package tndung.vnfb.smm.constant;

import lombok.experimental.UtilityClass;

@UtilityClass

public class Common {
    public static final String PASSWORD_PATTERN = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#&()–[{}]:;',?/*~$^+=<>]).{8,20}$";
    public static final String DATE_TIME_PATTERN = "dd/MM/yyyy HH:mm:ss";
    public static final Integer SCALE_DEFAULT = 10;
    public static final String DEFAULT_TIMEZONE = "+07:00";
    public static final String FULL_ACCESS_TENANT = "*";
    public static final String MAIN_TENANT = "0e22c37d-bfb5-4276-bd30-355fcdb39c9e";

    public static final String TENANT_HEADER = "X-Tenant-ID";


  /// public static final String DEFAULT_TIMEZONE = "UTC";
    @UtilityClass
    public class Redis {

        public static final String USER_LANG = "language:";
        public static final String MFA = "mfa:";
        public static final Long MFA_TTL = 300L;

        // I18n cache keys
        public static final String TRANSLATIONS = "translations:";
      public static final String DOMAIN_REQUEST_INFO = "domain_request_info:";
        public static final Long TRANSLATIONS_TTL = 86400L; // 24 hours

        // Distributed lock keys
        public static final String DRIPFEED_LOCK = "dripfeed:lock:";
        public static final Long DRIPFEED_LOCK_TTL = 300L; // 5 minutes
    }
}
