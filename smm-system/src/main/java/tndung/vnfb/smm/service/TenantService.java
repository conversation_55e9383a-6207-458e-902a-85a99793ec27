package tndung.vnfb.smm.service;

import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.dto.request.ChildTenantReq;
import tndung.vnfb.smm.dto.request.DomainReq;
import tndung.vnfb.smm.dto.request.ExtendTenantSubscriptionReq;
import tndung.vnfb.smm.dto.response.TenantHierarchyResponse;
import tndung.vnfb.smm.entity.Tenant;

import java.util.List;
import java.util.Optional;

public interface TenantService {
    Optional<Tenant> findByDomain(String domain);
    Optional<Tenant> findByTenantId(String tenantId);

    Optional<Tenant> findMainTenant();
    List<Tenant> findAll();

    boolean isMainTenantSite();

    String getTenantId();


    /**
     * Find all tenants with a specific status
     *
     * @param status The status to filter by
     * @return List of tenants with the specified status
     */
    List<Tenant> findByStatus(TenantStatus status);

    Tenant save(Tenant tenant);

    Tenant save(DomainReq tenant);
    void delete(String domain);

    /**
     * Update discount system enable status for current tenant
     */
    void updateDiscountSystemEnabled(boolean enabled);

    // Sub-tenant hierarchy methods (Single level: Parent -> Child only)

    /**
     * Find all direct sub-tenants of a parent tenant
     */
    List<Tenant> findSubTenants(String parentTenantId);

    /**
     * Find all parent tenants (tenants that can have sub-tenants)
     */
    List<Tenant> findAllParentTenants();

    /**
     * Find all sub-tenants (child tenants)
     */
    List<Tenant> findAllSubTenants();

    /**
     * Find parent tenant of a sub-tenant
     */
    Optional<Tenant> findParentTenant(String subTenantId);
    TenantHierarchyResponse getParentTenantHierarchy(String subTenantId);


    /**
     * Check if tenant can create more sub-tenants (only parent tenants can)
     */
    boolean canCreateSubTenant(String tenantId);



    /**
     * Check if tenant is a sub-tenant
     */
    boolean isSubTenant(String tenantId);




    TenantHierarchyResponse requestChildTenant(ChildTenantReq req);
    TenantHierarchyResponse approveSubTenant(String tenantId);
    TenantHierarchyResponse rejectSubTenant(String tenantId);

     TenantHierarchyResponse convertToHierarchyResponse(Tenant tenant);
    void deductUserBalance(int days);
    void suspendChildTenants(String tenantId);
    void activateChildTenants(String tenantId);
    TenantHierarchyResponse extendChildSubscription(String tenantId, ExtendTenantSubscriptionReq request);

    /**
     * Find all tenants owned by a specific user
     */
    List<Tenant> findTenantsByOwnerId(Long ownerId);
}
