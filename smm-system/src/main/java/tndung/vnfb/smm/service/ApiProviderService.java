package tndung.vnfb.smm.service;

import tndung.vnfb.smm.dto.request.ApiProviderEditReq;
import tndung.vnfb.smm.dto.request.ApiProviderReq;
import tndung.vnfb.smm.dto.response.ApiProviderRes;
import tndung.vnfb.smm.entity.ApiProvider;

import java.math.BigDecimal;
import java.util.List;

public interface ApiProviderService {


    ApiProviderRes checkBalance(Long id);

    void checkBalance(ApiProvider apiProvider);


    ApiProviderRes deactivate(Long id);

    ApiProviderRes active(Long id);

    ApiProviderRes add(ApiProviderReq req);

    ApiProvider findById(Long id);

    ApiProvider findByIsDeletedFalse(Long id);

    ApiProviderRes getById(Long id);
    ApiProviderRes edit(Long id, ApiProviderEditReq req);

    void changeApiKey(Long id,String apiKey);

    void changeBalanceAlert(Long id, BigDecimal balanceAlert);

    void delete(Long id);
    List<ApiProviderRes> getAllByTenant();

    List<ApiProvider> getAll();

    void checkAllBalance();

    ApiProvider save(ApiProvider apiProvider);

    ApiProvider findByTenantId(String tenantId);
}
