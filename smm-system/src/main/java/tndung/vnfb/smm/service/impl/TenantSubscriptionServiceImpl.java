package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.anotation.TenantAccessCheck;
import tndung.vnfb.smm.config.SubscriptionProperties;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.dto.RenewalRequest;
import tndung.vnfb.smm.dto.RenewalResponse;
import tndung.vnfb.smm.dto.TenantSubscriptionDto;
import tndung.vnfb.smm.entity.GTransaction;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.entity.UserTenantAccess;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.repository.nontenant.UserTenantAccessRepository;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.service.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantSubscriptionServiceImpl implements TenantSubscriptionService {

    private final TenantRepository tenantRepository;
    private final TenantService tenantService;
    private final PanelNotificationService notificationService;
    private final UserTenantAccessRepository userTenantAccessRepository;
    private final GUserRepository gUserRepository;
    private final TransactionRepository transactionRepository;
    private final SubscriptionProperties subscriptionProperties;

    private static final String AUTO_RENEWAL_SOURCE = "AUTO_RENEWAL";
    private static final String EXPIRATION_ALERT_EXPIRED = "Đã hết hạn";
    private static final String EXPIRATION_ALERT_SOON = "Sắp hết hạn";
    private static final String EXPIRATION_ALERT_NORMAL = "Bình thường";
    private static final String EXPIRATION_ALERT_LONG_TIME = "Còn lâu";

    @Transactional
    @Override
    @TenantAccessCheck
    public RenewalResponse renewSubscription(RenewalRequest request) {
        try {
            Optional<Tenant> tenantOpt = tenantService.findByTenantId(request.getTenantId());

            if (tenantOpt.isEmpty()) {
                return new RenewalResponse(false, "Tenant not found", null, request.getTenantId());
            }

            Tenant tenant = tenantOpt.get();
            ZonedDateTime newEndDate = calculateNewEndDate(tenant.getSubscriptionEndDate(), request.getExtensionDays());

            updateTenantSubscription(tenant, newEndDate);
            tenantService.save(tenant);

            log.info("Renewed subscription for tenant: {} until {}", request.getTenantId(), newEndDate);

            return new RenewalResponse(true, "Subscription renewed successfully", newEndDate, request.getTenantId());

        } catch (Exception e) {
            log.error("Error renewing subscription for tenant: {}", request.getTenantId(), e);
            return new RenewalResponse(false, "Error renewing subscription: " + e.getMessage(), null, request.getTenantId());
        }
    }

    @Transactional
    @Override
    public int updateExpiredTenants() {
        try {
            int expiredCount = tenantRepository.updateExpiredTenants();

            LocalDateTime gracePeriodEnd = LocalDateTime.now().minusDays(subscriptionProperties.getGracePeriodDays());
            int suspendedCount = tenantRepository.updateSuspendedTenants(gracePeriodEnd);

            log.info("Updated {} expired tenants and {} suspended tenants", expiredCount, suspendedCount);

            return expiredCount + suspendedCount;
        } catch (Exception e) {
            log.error("Error updating expired tenants", e);
            return 0;
        }
    }

    @Override
    public List<TenantSubscriptionDto> getTenantsExpiringSoon(int daysAhead) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureDate = now.plusDays(daysAhead);

        List<Tenant> tenants = tenantRepository.findTenantsExpiringSoon(TenantStatus.Active, now, futureDate);

        return tenants.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @TenantAccessCheck
    public TenantSubscriptionDto getTenantSubscription(String tenantId) {
        return tenantService.findByTenantId(tenantId)
                .map(this::convertToDto)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));
    }

    @Transactional
    @Override
    public void sendRenewalNotifications() {
        List<Tenant> expiringSoon = tenantRepository.findTenantsExpiringSoon(
                TenantStatus.Active,
                LocalDateTime.now(),
                LocalDateTime.now().plusDays(subscriptionProperties.getExpirationNotificationDays())
        );

        for (Tenant tenant : expiringSoon) {
            try {
                notificationService.createRenewalReminderNotification(tenant);
                tenant.setRenewalNotificationSent(true);
                tenantService.save(tenant);

                log.info("Sent renewal notification to tenant: {}", tenant.getId());
            } catch (Exception e) {
                log.error("Error sending renewal notification to tenant: {}", tenant.getId(), e);
            }
        }
    }

    @Transactional
    @Override
    @TenantAccessCheck
    public void updateAutoRenewal(String tenantId, Boolean autoRenewal) {
        Optional<Tenant> tenantOpt = tenantService.findByTenantId(tenantId);

        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }

        Tenant tenant = tenantOpt.get();
        tenant.setAutoRenewal(autoRenewal);
        tenantService.save(tenant);

        log.info("Updated auto-renewal for tenant: {} to {}", tenantId, autoRenewal);
    }

    @Transactional
    @Override
    public int processAutoRenewals() {
        try {
            List<Tenant> expiredTenantsWithAutoRenewal = tenantRepository.findExpiredTenantsWithAutoRenewal();
            int renewedCount = 0;

            for (Tenant tenant : expiredTenantsWithAutoRenewal) {
                try {
                    if (processAutoRenewalForTenant(tenant)) {
                        renewedCount++;
                    }
                } catch (Exception e) {
                    log.error("Error processing auto-renewal for tenant: {}", tenant.getId(), e);
                    // Continue with next tenant instead of failing the entire batch
                }
            }

            log.info("Successfully auto-renewed {} tenants", renewedCount);
            return renewedCount;

        } catch (Exception e) {
            log.error("Error in processAutoRenewals", e);
            return 0;
        }
    }

    /**
     * Process auto-renewal for a single tenant
     */
    private boolean processAutoRenewalForTenant(Tenant tenant) {
        List<UserTenantAccess> userAccesses = userTenantAccessRepository.findByTenantId(tenant.getId());

        if (userAccesses.isEmpty()) {
            log.warn("No users found for tenant: {}, skipping auto-renewal", tenant.getId());
            return false;
        }

        Long userId = userAccesses.get(0).getUserId();
        Optional<GUser> userOpt = gUserRepository.findById(userId);

        if (userOpt.isEmpty()) {
            log.warn("User {} not found for tenant: {}, skipping auto-renewal", userId, tenant.getId());
            return false;
        }

        GUser user = userOpt.get();
        BigDecimal renewalCost = subscriptionProperties.getRenewalCost();

        if (!hasSufficientBalance(user, renewalCost)) {
            log.warn("Insufficient balance for user {} (tenant: {}), skipping auto-renewal. Required: {}, Available: {}",
                    userId, tenant.getId(), renewalCost, user.getBalance());
            return false;
        }

        // Process payment and renewal
        processPaymentAndRenewal(user, tenant, renewalCost);

        log.info("Successfully auto-renewed tenant: {} for user: {}, cost: {}",
                tenant.getId(), userId, renewalCost);
        return true;
    }

    /**
     * Check if user has sufficient balance
     */
    private boolean hasSufficientBalance(GUser user, BigDecimal requiredAmount) {
        return user.getBalance().compareTo(requiredAmount) >= 0;
    }

    /**
     * Process payment and renewal for auto-renewal
     */
    private void processPaymentAndRenewal(GUser user, Tenant tenant, BigDecimal renewalCost) {
        // Deduct renewal cost from user balance
        user.setBalance(user.getBalance().subtract(renewalCost));
        gUserRepository.save(user);

        // Create transaction record
        createRenewalTransaction(user.getId(), tenant, renewalCost, user.getBalance());

        // Renew the tenant subscription
        ZonedDateTime newEndDate = ZonedDateTime.now().plusDays(subscriptionProperties.getDefaultRenewalDays());
        updateTenantSubscription(tenant, newEndDate);
        tenantService.save(tenant);
    }

    /**
     * Create transaction record for renewal
     */
    private void createRenewalTransaction(Long userId, Tenant tenant, BigDecimal renewalCost, BigDecimal newBalance) {
        GTransaction transaction = new GTransaction();
        transaction.setUserId(userId);
        transaction.setChange(renewalCost.negate()); // Negative amount for deduction
        transaction.setBalance(newBalance);
        transaction.setType(TransactionType.Subscription);
        transaction.setSource(AUTO_RENEWAL_SOURCE);
        transaction.setNote("Auto-renewal for tenant: " + tenant.getDomain());
        transactionRepository.save(transaction);
    }

    /**
     * Calculate new end date based on current end date and extension days
     */
    private ZonedDateTime calculateNewEndDate(ZonedDateTime currentEndDate, int extensionDays) {
        if (currentEndDate != null && currentEndDate.isAfter(ZonedDateTime.now())) {
            return currentEndDate.plusDays(extensionDays);
        } else {
            return ZonedDateTime.now().plusDays(extensionDays);
        }
    }

    /**
     * Update tenant subscription details
     */
    private void updateTenantSubscription(Tenant tenant, ZonedDateTime newEndDate) {
        tenant.setSubscriptionEndDate(newEndDate);
        tenant.setStatus(TenantStatus.Active);
        tenant.setLastRenewalDate(ZonedDateTime.now());
        tenant.setRenewalNotificationSent(false);
    }

    /**
     * Convert Tenant entity to DTO
     */
    private TenantSubscriptionDto convertToDto(Tenant tenant) {
        TenantSubscriptionDto dto = new TenantSubscriptionDto();
        dto.setId(tenant.getId());
        dto.setDomain(tenant.getDomain());
        dto.setStatus(tenant.getStatus().name());
        dto.setSubscriptionStartDate(tenant.getSubscriptionStartDate());
        dto.setSubscriptionEndDate(tenant.getSubscriptionEndDate());
        dto.setDaysUntilExpiration(tenant.getDaysUntilExpiration());
        dto.setAutoRenewal(tenant.getAutoRenewal());
        dto.setContactEmail(tenant.getContactEmail());
        dto.setExpirationAlert(getExpirationAlert(tenant.getDaysUntilExpiration()));

        return dto;
    }

    /**
     * Get expiration alert message based on days until expiration
     */
    private String getExpirationAlert(Integer daysUntilExpiration) {
        if (daysUntilExpiration == null) {
            return EXPIRATION_ALERT_NORMAL;
        }

        if (daysUntilExpiration < 0) {
            return EXPIRATION_ALERT_EXPIRED;
        } else if (daysUntilExpiration <= 7) {
            return EXPIRATION_ALERT_SOON;
        } else if (daysUntilExpiration <= 30) {
            return EXPIRATION_ALERT_NORMAL;
        } else {
            return EXPIRATION_ALERT_LONG_TIME;
        }
    }
}