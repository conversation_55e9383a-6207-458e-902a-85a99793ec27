package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.anotation.LogUserActivity;
import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.TemplateTypes;
import tndung.vnfb.smm.constant.enums.CommonStatus;
import tndung.vnfb.smm.constant.enums.Role;
import tndung.vnfb.smm.constant.enums.TransactionSource;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.dto.TokenPairDto;
import tndung.vnfb.smm.dto.connections.ConnectionSettingsDto;
import tndung.vnfb.smm.dto.connections.TelegramBotSettingsDto;
import tndung.vnfb.smm.dto.request.*;
import tndung.vnfb.smm.dto.response.*;
import tndung.vnfb.smm.dto.response.smm.SMMBalanceRes;
import tndung.vnfb.smm.entity.*;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.helper.CommonHelper;
import tndung.vnfb.smm.mapper.GUserMapper;
import tndung.vnfb.smm.mapper.SpecialPriceMapper;
import tndung.vnfb.smm.mapper.TransactionLogMapper;
import tndung.vnfb.smm.repository.GUserRepositoryCustom;
import tndung.vnfb.smm.repository.TransactionLogRepositoryCustom;
import tndung.vnfb.smm.repository.nontenant.EmailVerificationTokenRepository;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.tenant.*;
import tndung.vnfb.smm.service.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;

import static tndung.vnfb.smm.constant.Common.MAIN_TENANT;

@Service
@RequiredArgsConstructor
@Slf4j
public class GUserServiceImpl implements GUserService {
    private final GUserMapper gUserMapper;
    private final SpecialPriceMapper specialPriceMapper;

    private final GUserRepository gUserRepository;
    private final SpecialPriceRepository specialPriceRepository;
    private final BCryptPasswordEncoder bcryptEncoder;
    private final TransactionLogMapper transactionLogMapper;
    private final TransactionRepository transactionRepository;
    private final TransactionLogRepositoryCustom repositoryCustom;
    private final AuthenticationFacade authenticationFacade;

    private final GSvRepository gSvRepository;
    private final AffiliateService affiliateService;
    private final ReferralRepository referralRepo;
    private final CommissionRepository commissionRepo;
    private final CurrencyService currencyService;
    private final GUserRepositoryCustom gUserRepositoryCustom;
    private final KeyTokenService keyTokenService;
    private final UserTenantService userTenantService;
    private final TenantService tenantService;
    private final TenantSettingsService tenantSettingsService;
    private final BalanceService balanceService;
    private final UserNotificationService userNotificationService;
    @Value("${currency.api.base-currency}")
    private String baseCurrencyCode;

    private final TenantCurrencyService tenantCurrencyService;
    private final DiscountStatusService discountStatusService;
    private final PromotionService promotionService;
    private final PaymentMethodService paymentMethodService;
    private final TelegramService telegramService;
    private final ConnectionSettingsService connectionSettingsService;
    private final EmailService emailService;
    private final EmailVerificationTokenRepository emailVerificationTokenRepository;
    private final LanguageService languageService;
    private final ApiProviderService apiProviderService;

    public GUser depositProcess(Long userId, DepositReq req, PaymentMethod paymentMethod) {
        log.info("Processing deposit: userId={}, amount={}, source={}, externalId={}", userId, req.getAmount(), req.getSource(), req.getExternalTransactionId());

        // Retrieve the user by ID
        GUser user = findById(userId);

        BigDecimal depositAmount = req.getAmount();
        BigDecimal promotionBonusAmount = BigDecimal.ZERO;
        BigDecimal paymentBonusAmount = BigDecimal.ZERO;
        BigDecimal totalBonusAmount = BigDecimal.ZERO;

        // Check for active promotion and calculate promotion bonus
        Promotion activePromotion = promotionService.getLatestActivePromotion();
        if (activePromotion != null && activePromotion.getPromotionPercentage() != null) {
            promotionBonusAmount = depositAmount.multiply(activePromotion.getPromotionPercentage()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            log.info("Active promotion found: {}%, promotion bonus amount: {}", activePromotion.getPromotionPercentage(), promotionBonusAmount);
        }

        // Check for payment method bonus
        if (paymentMethod != null) {
            paymentBonusAmount = calculatePaymentMethodBonus(paymentMethod, depositAmount);
            if (paymentBonusAmount.compareTo(BigDecimal.ZERO) > 0) {
                log.info("Payment method bonus found: {}", paymentBonusAmount);
            }
        }

        // Calculate total bonus based on stackable setting
        if (activePromotion != null && activePromotion.isStackable()) {
            // Cộng dồn: promotion bonus + payment bonus
            totalBonusAmount = promotionBonusAmount.add(paymentBonusAmount);
            log.info("Stackable promotion: total bonus = promotion({}) + payment({})", promotionBonusAmount, paymentBonusAmount);
        } else {
            // Ưu tiên promotion bonus, nếu không có thì dùng payment bonus
            if (promotionBonusAmount.compareTo(BigDecimal.ZERO) > 0) {
                totalBonusAmount = promotionBonusAmount;
                log.info("Non-stackable: using promotion bonus: {}", promotionBonusAmount);
            } else {
                totalBonusAmount = paymentBonusAmount;
                log.info("Non-stackable: using payment bonus: {}", paymentBonusAmount);
            }
        }

        // Calculate total amount to add (deposit + total bonus)
        BigDecimal totalAmount = depositAmount.add(totalBonusAmount);

        // Update user balance
        BigDecimal oldBalance = user.getBalance();
        BigDecimal newBalance = oldBalance.add(totalAmount);
        user.setBalance(newBalance);

        // Save user first to get updated balance
        GUser savedUser = gUserRepository.save(user);

        // Create single transaction record for deposit with bonus included
        String depositNote = createDepositNoteWithBonus(req, depositAmount, promotionBonusAmount, paymentBonusAmount, activePromotion, paymentMethod);

        GTransaction depositTransaction = createTransactionWithOriginalAmount(savedUser.getId(), depositAmount.add(totalBonusAmount), // Total amount (deposit + bonus)
                newBalance, // Final balance
                depositAmount, // Original amount before bonus
                TransactionType.Deposit, req.getSource(), depositNote, null, // no order
                req.getExternalTransactionId(), paymentMethod != null ? paymentMethod.getId() : null // payment method ID
        );
        transactionRepository.save(depositTransaction);

        log.info("Deposit processed successfully: userId={}, depositAmount={}, promotionBonus={}, paymentBonus={}, totalBonus={}, oldBalance={}, newBalance={}, externalId={}", savedUser.getId(), depositAmount, promotionBonusAmount, paymentBonusAmount, totalBonusAmount, oldBalance, newBalance, req.getExternalTransactionId());

        // Process affiliate commission for the deposit (only on original deposit amount)
        processAffiliateCommission(savedUser, depositAmount, req.getExternalTransactionId());

        // Update user total deposit (only count original deposit, not bonus)
        updateUserTotalDeposit(savedUser);

        String tenantId = depositTransaction.getTenantId();

        // Send Telegram notification asynchronously
        sendDepositNotificationTelegramAsync(tenantId, depositTransaction, savedUser, paymentMethod);

        // Send Email notification asynchronously if user email is verified
        sendDepositNotificationEmailAsync(tenantId, depositTransaction, savedUser, paymentMethod);

        // Create deposit notification
        try {

            String formattedAmount = CommonHelper.formatPriceNotification(totalAmount);
            String formattedBalance = CommonHelper.formatPriceNotification(savedUser.getBalance());

            String depositNotificationContent = String.format("Bạn đã nạp thành công %s vào tài khoản. Số dư hiện tại: %s",
                    formattedAmount,
                    formattedBalance);
            userNotificationService.createDepositNotification(savedUser, depositNotificationContent);
        } catch (Exception e) {
            log.error("Failed to create deposit notification for user {}: {}", savedUser.getId(), e.getMessage());
        }

        // Return an appropriate response
        return savedUser;
    }


    @Override
    @Transactional
    public GUserRes deposit(Long userId, DepositReq req) {
        final PaymentMethod paymentMethod = paymentMethodService.findPaymentMethodById(req.getPaymentMethodId());
        return mapToUserSuperResWithEffectiveRate(depositProcess(userId, req, paymentMethod));
    }

    @Override
    public LanguageRes getLanguage() {
        return authenticationFacade.getCurrentUser().map(user -> LanguageRes.builder().lang(getUserLanguageFromCache(user.getId())).userId(user.getId()).build()).orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }

    @Cacheable(value = "userLanguage", key = "#userId", condition = "#userId != null")
    public String getUserLanguageFromCache(Long userId) {
        if (userId == null) {
            return "vi"; // Default to Vietnamese
        }

        if (tenantService.isMainTenantSite()) {
            TenantContext.setCurrentTenant(MAIN_TENANT);
        }

        GUser user = gUserRepository.findById(userId).orElseThrow(() -> new UsernameNotFoundException("User not found"));
        return user.getLanguage() != null ? user.getLanguage() : "vi"; // Default to Vietnamese
    }

    @Override
    @Transactional
    @CacheEvict(value = "userLanguage", key = "#result.userId")
    public LanguageRes setLanguage(LanguageReq lang) {
        // Validate language code exists and is active
        if (!languageService.isLanguageSupported(lang.getLang())) {
            throw new InvalidParameterException(IdErrorCode.LANG_INVALID);
        }

        return authenticationFacade.getCurrentUser().map(user -> {
            user.setLanguage(lang.getLang());
            gUserRepository.save(user);

            return LanguageRes.builder().lang(lang.getLang()).userId(user.getId()).build();
        }).orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }

    @Override
    public GUserRes setCurrency(CurrencyReq req) {
        GUser user = authenticationFacade.getCurrentUser().orElseThrow(() -> new UsernameNotFoundException("User not found"));
        Currency currency = currencyService.getCurrencyByCode(req.getCode());
        user.setPreferredCurrency(currency);
        return mapToUserResWithEffectiveRate(gUserRepository.save(user));
    }

    @Override
    public GUserRes setTimezone(TimezoneReq req) {
        GUser user = authenticationFacade.getCurrentUser().orElseThrow(() -> new UsernameNotFoundException("User not found"));
        ZoneOffset.of(req.getTimezone());
        user.setTimeZone(req.getTimezone());
        return mapToUserResWithEffectiveRate(gUserRepository.save(user));
    }

    @Override
    @Transactional
    public void refund(GOrder order) {
        // Use BalanceService to handle refund with automatic transaction logging
        balanceService.processRefund(order);
    }


    @Override
    public SMMBalanceRes getBalance() {
        GUser user = getCurrentUser();
        return mapToSMMBalanceRes(user);
    }

    private GUser getCurrentUser() {
        return authenticationFacade.getCurrentUser().orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }

    private SMMBalanceRes mapToSMMBalanceRes(GUser user) {
        return SMMBalanceRes.builder().balance(user.getBalance()).currency(baseCurrencyCode).build();
    }

    @Override
    public GUserRes deactivate(Long id) {
        final GUser user = findById(id);
        user.setStatus(CommonStatus.DEACTIVATED);
        return mapToUserResWithEffectiveRate(gUserRepository.save(user));
    }

    @Override
    public GUserRes active(Long id) {
        final GUser user = findById(id);
        user.setStatus(CommonStatus.ACTIVATED);
        return mapToUserResWithEffectiveRate(gUserRepository.save(user));
    }

    @Override
    public GUserPasswordRes resetPassword(Long id) {
        final GUser user = findById(id);
        final String randomPassword = CommonHelper.generateRandomText(15);
        user.setPassword(bcryptEncoder.encode(randomPassword));
        gUserRepository.save(user);

        // logout
        keyTokenService.findByUserIdForUserSite(id).forEach(key -> keyTokenService.deleteById(key.getId()));

        return GUserPasswordRes.builder().newPassword(randomPassword).build();
    }

    @Override
    @Transactional
    public ApiKeyUserRes generateApiKey() {
        GUser user = getCurrentUser();
        user.setApiKey(CommonHelper.generateNewToken());
        updateKeyForProviderSubPanel(user, user.getApiKey());
        return mapToApiKeyUserResWithEffectiveRate(gUserRepository.save(user));
    }

    private void updateKeyForProviderSubPanel(GUser user, String key) {
        List<Tenant> tenants = tenantService.findTenantsByOwnerId(user.getId());
        for (Tenant tenant : tenants) {
            ApiProvider apiProvider = apiProviderService.findByTenantId(tenant.getId());
            if (apiProvider == null) continue;
            apiProvider.setSecretKey(key);
            apiProviderService.save(apiProvider);
        }
    }


    @Override
    @Transactional
    public void changePassword(UserChangePassReq req) {
        GUser user = getCurrentUser();
        validatePassword(req.getOldPassword(), user.getPassword());
        user.setPassword(bcryptEncoder.encode(req.getNewPassword()));
        gUserRepository.save(user);
    }

    @Override
    @Transactional
    public void sendEmailVerification() {
        GUser user = getCurrentUser();
        if (Boolean.TRUE.equals(user.getVerified())) {
            throw new InvalidParameterException(IdErrorCode.USER_PHONE_EXISTS);
        }
        String tenantId = TenantContext.getSiteTenant();
        Tenant tenant = tenantService.findByTenantId(tenantId).orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        try {
            // Mark all existing tokens as used for this user
            emailVerificationTokenRepository.markAllTokensAsUsedForUser(user.getId());

            // Generate verification token
            String verificationToken = CommonHelper.generateNewToken();

            // Create and save verification token
            EmailVerificationToken token = new EmailVerificationToken();
            token.setToken(verificationToken);
            token.setUserId(user.getId());
            token.setEmail(user.getEmail());
            token.setExpiresAt(OffsetDateTime.now().plusHours(24)); // 24 hours expiry
            token.setUsed(false);
            token.setTenantId(tenantId);
            emailVerificationTokenRepository.save(token);

            // Prepare template variables
            Map<String, String> variables = new HashMap<>();
            variables.put("username", user.getUserName());
            variables.put("email", user.getEmail());
            variables.put("verification_link", generateVerificationLink(tenantId, verificationToken));
            variables.put("domain", tenant.getDomain());
            variables.put("support_email", "support@" + tenant.getDomain());

            // Send email using template
            emailService.sendTemplateEmail(tenantId, user.getEmail(), TemplateTypes.Email.EMAIL_VERIFICATION, variables);

            log.info("Email verification sent successfully to: {}", user.getEmail());
        } catch (Exception e) {
            log.error("Failed to send email verification to: {}", user.getEmail(), e);
            throw new RuntimeException("Failed to send verification email");
        }
    }

    @Override
    @Transactional
    public void confirmEmailVerification(String token) {


        // Find the token
        EmailVerificationToken verificationToken = emailVerificationTokenRepository.findByTokenAndUsedFalse(token).orElseThrow(() -> new InvalidParameterException(IdErrorCode.TOKEN_INVALID_OR_EXPIRED));

        // Check if token is valid
        if (!verificationToken.isValid()) {
            throw new InvalidParameterException(IdErrorCode.TOKEN_EXPIRED_OR_USED);
        }

        // Find the user
        GUser user = gUserRepository.findByIdAndTenantId(verificationToken.getTenantId(), verificationToken.getUserId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Verify email matches
        if (!user.getEmail().equals(verificationToken.getEmail())) {
            throw new InvalidParameterException(IdErrorCode.EMAIL_MISMATCH);
        }

        // Mark user as verified
        user.setVerified(true);
        gUserRepository.save(user);

        // Mark token as used
        verificationToken.setUsed(true);
        emailVerificationTokenRepository.save(verificationToken);

        log.info("Email verification completed successfully for user: {}", user.getUserName());


    }

    private String generateVerificationLink(String tenantId, String token) {
        Tenant tenant = tenantService.findByTenantId(tenantId).orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));
        // Generate link to dashboard verification page instead of API endpoint
        return String.format("https://%s/auth/verify-email?token=%s", tenant.getDomain(), token);
    }

    @Override
    @Transactional
    public GUserRes edit(EditUserReq req) {
        GUser user = getCurrentUser();

        return mapToUserResWithEffectiveRate(edit(user, req));
    }

    private GUser edit(GUser user, EditUserReq req) {
        verifyUniqueEmailAndPhone(req.getEmail(), req.getPhone(), user);

        // Check if email is being changed
        boolean emailChanged = !user.getEmail().equals(req.getEmail());

        user.setEmail(req.getEmail());

        // Only update phone if it's provided (not null or empty)
        if ((req.getPhone() != null && !req.getPhone().trim().isEmpty()) || user.getPhone() != null) {
            user.setPhone(req.getPhone());
        }


        // If email changed, set verified to false
        if (emailChanged) {
            user.setVerified(false);
            log.info("Email changed for user: {}, setting verified to false", user.getUserName());
        }

        return gUserRepository.save(user);

    }

    private void validatePassword(String oldPassword, String currentPassword) {
        if (!bcryptEncoder.matches(oldPassword, currentPassword)) {
            throw new InvalidParameterException(IdErrorCode.OLD_PASSWORD_WRONG);
        }
    }

    private void verifyUniqueEmailAndPhone(String email, String phone, GUser user) {
        if (!email.equals(user.getEmail()) && gUserRepository.findByEmail(email).isPresent()) {
            throw new InvalidParameterException(IdErrorCode.USER_EMAIL_EXISTS);
        }

        // Only check phone uniqueness if phone is provided and different from current
        if (phone != null && !phone.trim().isEmpty() &&
                !phone.equals(user.getPhone()) && gUserRepository.findByPhone(phone).isPresent()) {
            throw new InvalidParameterException(IdErrorCode.USER_PHONE_EXISTS);
        }
    }


    @Override
    @Transactional
    @LogUserActivity(operation = "CREATE", entity = "UserReq")
    public GUserRes create(UserReq req) {
        if (gUserRepository.findByUserName(req.getUserName()).isPresent())
            throw new InvalidParameterException(IdErrorCode.USER_NAME_EXISTS);

        if (gUserRepository.findByEmail(req.getEmail()).isPresent())
            throw new InvalidParameterException(IdErrorCode.USER_EMAIL_EXISTS);


        if (!Strings.isBlank(req.getPhone())) {
            if (gUserRepository.findByPhone(req.getPhone()).isPresent())
                throw new InvalidParameterException(IdErrorCode.USER_PHONE_EXISTS);
        }


        final GUser user = gUserMapper.toEntity(req);
        user.setPassword(bcryptEncoder.encode(req.getPassword()));
        user.setAvatar("profile-" + (new Random().nextInt(10) + 1));
        user.setPreferredCurrency(currencyService.getBaseCurrency());
        final String lang = tenantSettingsService.getTenantDefaultLanguage().getDefaultLanguage();
        user.setLanguage(lang);
        if (tenantService.isMainTenantSite()) {
            user.setRoles(List.of(Role.PANEL));

            final GUser savedUser = gUserRepository.save(user);
            userTenantService.save(savedUser.getId(), MAIN_TENANT);
            return createUserPanel(savedUser);
        } else {
            user.setRoles(List.of(Role.USER));
            final GUser savedUser = gUserRepository.save(user);
            userTenantService.save(savedUser.getId(), TenantContext.getSiteTenant());
            return createUserNormal(savedUser, req.getReferralCode());
        }

    }

    private GUserRes createUserPanel(GUser user) {
        final TokenPairDto tokenPairDto = keyTokenService.createKeyPairPanel(user, null);
        final GUserRes userRes = mapToUserResWithEffectiveRate(user);
        userRes.setTokens(tokenPairDto);
        return userRes;
    }

    private GUserRes createUserNormal(GUser user, String referralCode) {
        final TokenPairDto tokenPairDto = keyTokenService.createKeyPairUser(user);
        final GUserRes userRes = mapToUserResWithEffectiveRate(user);
        userRes.setTokens(tokenPairDto);
        affiliateService.createAffiliate(user);
        if (!Strings.isBlank(referralCode) && tenantSettingsService.isEnableAffiliate()) {
            affiliateService.createReferral(user, referralCode);
        }
        return userRes;
    }

    @Override
    public GUser findByEmail(String userName) {
        return gUserRepository.findByUserName(userName).orElseThrow(() -> new UsernameNotFoundException("email not found"));
    }

    @Override
    public GUser findById(Long id) {
        return gUserRepository.findById(id).orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));
    }

    @Override
    @Transactional
    public TransactionRes addFund(Long id, AddFundReq addFundReq) {
        final GUser user = findById(id);

        // Check if this is a bonus or a payment method deposit
        String source = addFundReq.getSource().name();
        boolean isBonus = TransactionSource.BONUS.equals(addFundReq.getSource());

        GUser updatedUser;
        if (BigDecimal.ZERO.compareTo(addFundReq.getAmount()) < 0) {
            if (isBonus) {
                // Use BalanceService for bonus transactions
                updatedUser = balanceService.addBalance(user, addFundReq.getAmount(), TransactionType.Bonus, source, addFundReq.getNote());

                // Create bonus notification
                try {
                    String bonusNotificationContent = String.format("Bạn đã nhận được thưởng %s. %s. Số dư hiện tại: %s",
                            CommonHelper.formatPriceNotification(addFundReq.getAmount()),
                            addFundReq.getNote() != null ? addFundReq.getNote() : "",
                            CommonHelper.formatPriceNotification(updatedUser.getBalance()));
                    userNotificationService.createBonusNotification(updatedUser, bonusNotificationContent);
                } catch (Exception e) {
                    log.error("Failed to create bonus notification for user {}: {}", updatedUser.getId(), e.getMessage());
                }
            } else {
                // Use admin deposit method for payment method transactions
                //String externalTransactionId = "admin-" + System.currentTimeMillis();

                final PaymentMethod paymentMethod = paymentMethodService.findPaymentMethodById(id);
                updatedUser = depositProcess(user.getId(), DepositReq.builder().source(source).amount(addFundReq.getAmount()).build(), paymentMethod);
            }
        } else {
            BigDecimal amount = addFundReq.getAmount().abs();
            updatedUser = balanceService.deductBalance(user, amount, TransactionType.Remove, source, addFundReq.getNote());
        }

        // Get the latest transaction for this operation
        final GTransaction latestTransaction = transactionRepository.findLatestByUserId(updatedUser.getId()).orElseThrow(() -> new InvalidParameterException(IdErrorCode.TRANSACTION_NOT_FOUND));

        final TransactionRes transactionRes = transactionLogMapper.toRes(latestTransaction);
        transactionRes.setBalance(updatedUser.getBalance());

        return transactionRes;
    }

    @Override
    public Optional<GUser> findByApiKey(String apiKey) {
        return gUserRepository.findByApiKey(apiKey);
    }

    @Override
    public GUserRes getInfo() {
        return authenticationFacade.getCurrentUser().flatMap(user -> gUserRepository.findByIdWithTotalDeposit(user.getId())).map(this::mapToUserResWithEffectiveRate).orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }

    /**
     * Helper method to map GUser to GUserRes with effective currency rate and calculated title rank
     */
    private GUserRes mapToUserResWithEffectiveRate(GUser user) {
        GUserRes userRes = gUserMapper.toRes(user);
        updateCurrencyWithEffectiveRate(userRes, user);

        // Calculate title rank based on total deposit
        if (user.getTotalDeposit() != null) {
            DiscountStatusRes titleRank = discountStatusService.findApplicableDiscountStatus(user.getTotalDeposit());
            userRes.setTitleRank(titleRank);
        }

        return userRes;
    }

    /**
     * Helper method to map GUser to GUserSuperRes with effective currency rate and calculated title rank
     */
    private GUserSuperRes mapToUserSuperResWithEffectiveRate(GUser user) {
        GUserSuperRes userRes = gUserMapper.toSuperRes(user);
        updateCurrencyWithEffectiveRate(userRes, user);

        // Calculate title rank based on total deposit
        if (user.getTotalDeposit() != null && user.getTotalDeposit().compareTo(BigDecimal.ZERO) > 0) {
            DiscountStatusRes titleRank = discountStatusService.findApplicableDiscountStatus(user.getTotalDeposit());
            userRes.setTitleRank(titleRank);
        }

        return userRes;
    }

    /**
     * Helper method to map GUser to ApiKeyUserRes with effective currency rate
     */
    private ApiKeyUserRes mapToApiKeyUserResWithEffectiveRate(GUser user) {
        ApiKeyUserRes userRes = gUserMapper.toApiKey(user);
        updateCurrencyWithEffectiveRate(userRes, user);
        return userRes;
    }

    /**
     * Updates the preferred currency in user response with effective rate
     * This method works with any user response object that has a setPreferredCurrency method
     */
    private void updateCurrencyWithEffectiveRate(Object userRes, GUser user) {
        if (user.getPreferredCurrency() != null) {
            String tenantId = TenantContext.getCurrentTenant();
            String currencyCode = user.getPreferredCurrency().getCode();

            // Get effective rate (custom rate if sync disabled, otherwise standard rate)
            BigDecimal effectiveRate = tenantCurrencyService.getEffectiveRate(tenantId, currencyCode);

            // Create updated currency response with effective rate
            CurrencyRes updatedCurrency = new CurrencyRes();
            updatedCurrency.setCode(user.getPreferredCurrency().getCode());
            updatedCurrency.setSymbol(user.getPreferredCurrency().getSymbol());
            updatedCurrency.setExchangeRate(effectiveRate);

            // Use reflection to set the preferred currency on any user response type
            try {
                userRes.getClass().getMethod("setPreferredCurrency", CurrencyRes.class).invoke(userRes, updatedCurrency);
            } catch (Exception e) {
                log.warn("Could not update preferred currency for user response type: {}", userRes.getClass().getSimpleName(), e);
            }
        }
    }

    @Override
    public ApiKeyUserRes getApiKey() {
        GUser user = authenticationFacade.getCurrentUser().orElseThrow(() -> new UsernameNotFoundException("Current user not found"));
        String apiKey = user.getApiKey();
        if (Strings.isNotBlank(apiKey)) {
            user.setApiKey(CommonHelper.maskEndCharacters(apiKey, 20));
        }

        return mapToApiKeyUserResWithEffectiveRate(user);
    }

    @Override
    public Page<GUserSuperRes> search(UserSearchReq req, Pageable pageable) {
        return gUserRepositoryCustom.search(req, pageable);
    }

    /**
     * Helper method to update an existing GUserSuperRes with effective currency rate
     */
    private GUserSuperRes updateUserSuperResWithEffectiveRate(GUserSuperRes userRes) {
        if (userRes.getPreferredCurrency() != null) {
            String tenantId = TenantContext.getCurrentTenant();
            String currencyCode = userRes.getPreferredCurrency().getCode();

            // Get effective rate (custom rate if sync disabled, otherwise standard rate)
            BigDecimal effectiveRate = tenantCurrencyService.getEffectiveRate(tenantId, currencyCode);

            // Update the existing currency response with effective rate
            userRes.getPreferredCurrency().setExchangeRate(effectiveRate);
        }
        // Calculate title rank based on total deposit
        if (userRes.getTotalDeposit() != null) {
            DiscountStatusRes titleRank = discountStatusService.findApplicableDiscountStatus(userRes.getTotalDeposit());
            userRes.setTitleRank(titleRank);
        }
        return userRes;
    }

    @Override
    public GUserSuperRes getDetailUser(Long id) {
        final GUser user = findById(id);
        return mapToUserSuperResWithEffectiveRate(user);
    }

    @Override
    public Page<TransactionRes> getTransactionsByUser(Long id, TransactionSearchReq req, Pageable pageable) {
        SearchParams searchParams = buildSearchParams(req.getFrom(), req.getTo());
        // Repository now returns Page<TransactionRes> directly
        return repositoryCustom.searchTransactions(id, req.getTypes(), req.getOrderId(),
                searchParams.from, searchParams.to, pageable);
    }

    @Override
    public Page<MyTransactionRes> getMyTransactions(TransactionSearchReq req, Pageable pageable) {
        GUser user = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new UsernameNotFoundException("Current user not found"));

        SearchParams searchParams = buildSearchParams(req.getFrom(), req.getTo());

        // Use the optimized method that includes order information in single query
        return repositoryCustom.searchMyTransactions(
                user.getId(), req.getTypes(), req.getOrderId(),
                searchParams.from, searchParams.to, pageable);
    }

    private record SearchParams(OffsetDateTime from, OffsetDateTime to) {
    }


    private SearchParams buildSearchParams(LocalDate from, LocalDate to) {
        ZoneId userTimezone = AuditContextHolder.getUserZone();
        OffsetDateTime startDate = CommonHelper.getStartDay(from, userTimezone);
        OffsetDateTime endDate = CommonHelper.getEndDay(to, userTimezone);

        return new SearchParams(startDate, endDate);
    }


    @Override
    @Transactional
    public GUserSuperRes superEdit(Long id, EditUserReq req) {
        final GUser user = findById(id);
        boolean emailChanged = !user.getEmail().equals(req.getEmail());
        if (emailChanged) {
            user.setVerified(false);
            log.info("Email changed for user: {}, setting verified to false", user.getUserName());
        }
        return mapToUserSuperResWithEffectiveRate(edit(user, req));
    }

    @Override
    @Transactional
    public GUserSuperRes addAllCustomDiscount(Long id, CustomDiscountReq req) {
        if (req.getCustomDiscount().compareTo(new BigDecimal(100)) > 0 || req.getCustomDiscount().compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParameterException(IdErrorCode.DISCOUNT_NOT_VALID);
        }
        final GUser user = findById(id);
        user.setCustomDiscount(req.getCustomDiscount());
        return mapToUserSuperResWithEffectiveRate(gUserRepository.save(user));
    }

    @Override
    @Transactional
    public GUserSuperRes customReferral(Long id, CustomReferralReq req) {
        final GUser user = findById(id);
        user.setCustomReferralRate(req.getCustomRate());
        return mapToUserSuperResWithEffectiveRate(gUserRepository.save(user));
    }

    @Override
    @Transactional
    public SpecialPriceRes addCustomDiscount(Long id, CustomDiscountServiceReq customDiscount) {

        if (SpecialPrice.DiscountType.PERCENT.equals(customDiscount.getType()) && customDiscount.getCustomDiscount().compareTo(new BigDecimal(100)) >= 0 || customDiscount.getCustomDiscount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParameterException(IdErrorCode.PRICE_SPECIAL_LESS_THAN_ORIGINAL);
        }


        final GUser user = findById(id);
        GService gService = gSvRepository.findById(customDiscount.getServiceId()).orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));

        if (SpecialPrice.DiscountType.FIXED.equals(customDiscount.getType()) && customDiscount.getCustomDiscount().compareTo(gService.getOriginalPrice()) >= 0) {
            throw new InvalidParameterException(IdErrorCode.PRICE_SPECIAL_LESS_THAN_ORIGINAL);
        }

        final Optional<SpecialPrice> specialPriceOpt = specialPriceRepository.findByUserAndService(user, gService);
        SpecialPrice specialPrice;
        if (specialPriceOpt.isPresent()) {
            specialPrice = specialPriceOpt.get();
            specialPrice.setDiscountValue(customDiscount.getCustomDiscount());
            specialPrice.setDiscountType(customDiscount.getType());
        } else {
            specialPrice = new SpecialPrice();
            specialPrice.setDiscountType(customDiscount.getType());
            specialPrice.setDiscountValue(customDiscount.getCustomDiscount());

            specialPrice.setService(gService);
            specialPrice.setUser(user);
        }

        return specialPriceMapper.toRes(specialPriceRepository.save(specialPrice));
    }

    @Override
    public List<SpecialPriceRes> getCustomDiscountByUser(Long userId) {

        final GUser user = findById(userId);
        final List<SpecialPrice> specialPrices = specialPriceRepository.findByUser(user);
        return specialPriceMapper.toRes(specialPrices);
    }

    @Override
    public List<SpecialPriceRes> getCustomDiscountByService(Long serviceId) {
        final GService service = gSvRepository.findById(serviceId).orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));
        final List<SpecialPrice> specialPrices = specialPriceRepository.findByService(service);
        return specialPriceMapper.toRes(specialPrices);
    }

    @Override
    public List<SpecialPriceCountRes> getCountCustomDiscountByService() {

        return specialPriceRepository.countByServiceId();

    }


    @Override
    public SpecialPriceRes editCustomDiscount(Long id, CustomDiscountServiceReq req) {
        SpecialPrice specialPrice = specialPriceRepository.findById(id).orElseThrow(() -> new InvalidParameterException(IdErrorCode.SPECIAL_PRICE_NOT_FOUND));

        if (SpecialPrice.DiscountType.PERCENT.equals(req.getType()) && req.getCustomDiscount().compareTo(new BigDecimal(100)) >= 0 || req.getCustomDiscount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParameterException(IdErrorCode.DISCOUNT_NOT_VALID);
        }

        GService gService = gSvRepository.findById(req.getServiceId()).orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));

        if (SpecialPrice.DiscountType.FIXED.equals(req.getType()) && req.getCustomDiscount().compareTo(gService.getOriginalPrice()) >= 0) {
            throw new InvalidParameterException(IdErrorCode.DISCOUNT_NOT_VALID);
        }

        specialPrice.setDiscountType(req.getType());
        specialPrice.setDiscountValue(req.getCustomDiscount());
        specialPrice.setService(gService);

        return specialPriceMapper.toRes(specialPriceRepository.save(specialPrice));
    }

    @Override
    public void deleteCustomDiscount(Long id) {
        SpecialPrice specialPrice = specialPriceRepository.findById(id).orElseThrow(() -> new InvalidParameterException(IdErrorCode.SPECIAL_PRICE_NOT_FOUND));
        specialPriceRepository.delete(specialPrice);
    }

    @Override
    @Transactional
    public void deleteCustomDiscountByUser(Long userId) {
        final GUser user = findById(userId);

        List<SpecialPrice> specialPrices = specialPriceRepository.findByUser(user);
        specialPriceRepository.deleteAll(specialPrices);
    }

    @Override
    @Transactional
    public void deleteCustomDiscountByService(Long serviceId) {
        GService gService = gSvRepository.findById(serviceId).orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));

        List<SpecialPrice> specialPrices = specialPriceRepository.findByService(gService);
        specialPriceRepository.deleteAll(specialPrices);
    }

    @Override
    public MySummaryTransaction summaryMyTransactions() {
        Optional<GUser> currentUser = authenticationFacade.getCurrentUser();
        if (currentUser.isEmpty()) {
            throw new UsernameNotFoundException("User not found");
        }

        return repositoryCustom.summaryMyTransactions(currentUser.get().getId());
    }

    @Override
    public SummaryTransaction summaryTransactions(Long userId) {
        final GUser user = findById(userId);
        return repositoryCustom.summaryTransactions(user.getId());
    }

    /**
     * Create transaction record with external transaction ID
     */
    private GTransaction createTransaction(Long userId, BigDecimal amount, BigDecimal newBalance, String source, String note, GOrder order, String externalTransactionId) {
        return createTransaction(userId, amount, newBalance, TransactionType.Bonus, source, note, order, externalTransactionId, null);
    }

    /**
     * Create transaction record with external transaction ID and payment method ID
     */
    private GTransaction createTransaction(Long userId, BigDecimal amount, BigDecimal newBalance, TransactionType type, String source, String note, GOrder order, String externalTransactionId, Long paymentMethodId) {
        GTransaction transaction = new GTransaction();
        transaction.setUserId(userId);
        transaction.setChange(amount); // Use 'change' field instead of 'amount'
        transaction.setBalance(newBalance);
        transaction.setType(type);
        transaction.setSource(source);
        transaction.setNote(note);
        transaction.setOrder(order); // Use 'order' field instead of 'orderId'
        transaction.setExternalTransactionId(externalTransactionId);
        transaction.setPaymentMethodId(paymentMethodId);
        return transaction;
    }

    /**
     * Create transaction record with original amount for deposit transactions
     */
    private GTransaction createTransactionWithOriginalAmount(Long userId, BigDecimal amount, BigDecimal newBalance, BigDecimal originalAmount, TransactionType type, String source, String note, GOrder order, String externalTransactionId, Long paymentMethodId) {
        GTransaction transaction = new GTransaction();
        transaction.setUserId(userId);
        transaction.setChange(amount); // Total amount (deposit + bonus)
        transaction.setBalance(newBalance);
        transaction.setOriginalAmount(originalAmount); // Original deposit amount before bonus
        transaction.setType(type);
        transaction.setSource(source);
        transaction.setNote(note);
        transaction.setOrder(order);
        transaction.setExternalTransactionId(externalTransactionId);
        transaction.setPaymentMethodId(paymentMethodId);

        return transaction;
    }

    /**
     * Process affiliate commission for deposit transaction
     */
    private void processAffiliateCommission(GUser user, BigDecimal depositAmount, String externalTransactionId) {
        log.debug("Processing affiliate commission for user: {}, amount: {}", user.getUserName(), depositAmount);

        // Check if affiliate feature is enabled
        if (!tenantSettingsService.isEnableAffiliate()) {
            log.debug("Affiliate feature is disabled, skipping commission processing");
            return;
        }

        // Find if this user was referred
        Optional<Referral> referralOpt = referralRepo.findByReferredUser(user);
        if (referralOpt.isEmpty()) {
            log.debug("User {} was not referred, no commission to process", user.getUserName());
            return;
        }

        Referral referral = referralOpt.get();
        Affiliate affiliate = referral.getAffiliate();

        // Get effective commission rate using the same logic as AffiliateServiceImpl
        BigDecimal commissionRate = getEffectiveCommissionRate(affiliate.getUser());
        if (commissionRate.compareTo(BigDecimal.ZERO) <= 0) {
            log.debug("Commission rate is 0 or negative, no commission to process");
            return;
        }

        BigDecimal commissionAmount = depositAmount.multiply(commissionRate).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        // Create commission record with PAID status and immediately process payment
        Commission commission = new Commission();
        commission.setAffiliateId(affiliate.getId());
        commission.setCommissionAmount(commissionAmount);
        commission.setStatus(Commission.Status.PAID); // Set status to PAID immediately

        // Add balance to affiliate user and create transaction
        GUser affiliateUser = affiliate.getUser();
        String commissionNote = String.format("Commission payment: $%s from referral %s", commissionAmount, user.getUserName());
        balanceService.addBalance(affiliateUser, commissionAmount, TransactionType.Affiliate, "Affiliate", commissionNote);

        // Find the transaction that was just created and link it to commission
        Optional<GTransaction> transactionOpt = transactionRepository.findLatestByUserId(affiliateUser.getId());
        transactionOpt.ifPresent(gTransaction -> commission.setTransactionId(gTransaction.getId()));

        // Save the commission with PAID status and transaction ID
        commissionRepo.save(commission);

        log.info("Successfully processed commission payment for affiliate {}: amount={} USD, rate={}%, transactionId={}, referralUser={}", affiliate.getUser().getUserName(), commissionAmount, commissionRate, externalTransactionId, user.getUserName());
    }

    /**
     * Get effective commission rate - same logic as AffiliateServiceImpl
     */
    private BigDecimal getEffectiveCommissionRate(GUser user) {
        // Use custom rate if set, otherwise use tenant default
        if (user.getCustomReferralRate() != null && user.getCustomReferralRate().compareTo(BigDecimal.ZERO) > 0) {
            return user.getCustomReferralRate();
        }

        Optional<Tenant> tenantOpt = tenantService.findByTenantId(TenantContext.getWildcardTenant());
        if (tenantOpt.isPresent() && tenantOpt.get().getPercentageAffiliate() != null) {
            return tenantOpt.get().getPercentageAffiliate();
        }

        return BigDecimal.ZERO;
    }


    /**
     * Update user total deposit amount
     */
    private void updateUserTotalDeposit(GUser user) {
        try {
            // Calculate total deposit amount for the user
            BigDecimal totalDeposit = transactionRepository.getTotalDepositByUserId(user.getId());

            log.debug("Updating total deposit for user: {}, total deposit: {}", user.getId(), totalDeposit);

            // Update user's total deposit
            user.setTotalDeposit(totalDeposit);
            gUserRepository.save(user);

            log.info("Updated total deposit for user: {} to amount: {}", user.getId(), totalDeposit);
        } catch (Exception e) {
            log.error("Error updating total deposit for user: {}", user.getId(), e);
            // Don't throw exception to avoid breaking the deposit process
        }
    }

    /**
     * Get user's current rank based on total deposit
     */
    public DiscountStatusRes getUserRankByTotalDeposit(GUser user) {
        if (user.getTotalDeposit() == null) {
            return null;
        }
        return discountStatusService.findApplicableDiscountStatus(user.getTotalDeposit());
    }

    /**
     * Calculate payment method bonus amount
     */
    private BigDecimal calculatePaymentMethodBonus(PaymentMethod paymentMethod, BigDecimal depositAmount) {
        if (paymentMethod.getConditions() == null || paymentMethod.getConditions().isEmpty()) {
            log.debug("No bonus conditions configured for payment method: {}", paymentMethod.getId());
            return BigDecimal.ZERO;
        }

        PaymentMethodCondition bestCondition = paymentMethodService.findBestBonusCondition(paymentMethod.getConditions(), depositAmount);

        if (bestCondition == null) {
            log.debug("No applicable bonus condition found for amount: {} USD", depositAmount);
            return BigDecimal.ZERO;
        }

        BigDecimal bonusAmount = depositAmount.multiply(bestCondition.getBonusAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        if (bonusAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        return bonusAmount;
    }

    /**
     * Create deposit note with bonus information
     */
    private String createDepositNoteWithBonus(DepositReq req, BigDecimal depositAmount, BigDecimal promotionBonusAmount, BigDecimal paymentBonusAmount, Promotion activePromotion, PaymentMethod paymentMethod) {
        StringBuilder noteBuilder = new StringBuilder();
        noteBuilder.append(String.format("Deposit via %s: %s USD", req.getSource(), depositAmount));

        // Add promotion bonus info
        if (promotionBonusAmount.compareTo(BigDecimal.ZERO) > 0 && activePromotion != null) {
            noteBuilder.append(String.format(" + Promotion bonus (%s%%): %s USD", activePromotion.getPromotionPercentage(), promotionBonusAmount));
        }

        // Add payment method bonus info
        if (paymentBonusAmount.compareTo(BigDecimal.ZERO) > 0 && paymentMethod != null) {
            PaymentMethodCondition bestCondition = paymentMethodService.findBestBonusCondition(paymentMethod.getConditions(), depositAmount);
            if (bestCondition != null) {
                noteBuilder.append(String.format(" + Payment bonus (%s%%): %s USD", bestCondition.getBonusAmount(), paymentBonusAmount));
            } else {
                noteBuilder.append(String.format(" + Payment bonus: %s USD", paymentBonusAmount));
            }
        }

        // Add total if there's any bonus
        BigDecimal totalBonus = promotionBonusAmount.add(paymentBonusAmount);
        if (totalBonus.compareTo(BigDecimal.ZERO) > 0) {
            noteBuilder.append(String.format(" = Total: %s USD", depositAmount.add(totalBonus)));
        }

        return noteBuilder.toString();
    }

    /**
     * Send deposit notification to Telegram asynchronously
     */
    @Async
    protected void sendDepositNotificationTelegramAsync(String tenantId, GTransaction transaction, GUser user, PaymentMethod paymentMethod) {
        try {


            // Get connection settings
            ConnectionSettingsDto connectionSettings = connectionSettingsService.getSettingsById(tenantId);
            TelegramBotSettingsDto telegramSettings = connectionSettings.getTelegramBot();

            // Prepare notification data
            String transactionId = transaction.getExternalTransactionId() != null ? transaction.getExternalTransactionId() : transaction.getId().toString();
            String amount = transaction.getOriginalAmount() != null ? transaction.getOriginalAmount().toString() : transaction.getChange().toString();
            String currency = user.getPreferredCurrency().getCode(); // Base currency

            String method = paymentMethod != null ? paymentMethod.getName() : transaction.getSource();
            String depositTime = CommonHelper.formatTimeToNotification(transaction.getCreatedAt(), user.getUserZone());

            final Tenant tenant = tenantService.findByTenantId(tenantId).orElseThrow(()
                    -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));


            // Send notification with deposit data
            telegramService.sendDepositNotification(telegramSettings, tenantId, transactionId, amount, currency, method,
                    depositTime, user.getUserName(), tenant.getDomain());
        } catch (Exception e) {
            log.error("Failed to send Telegram notification for deposit: transactionId={}, error: {}", transaction.getId(), e.getMessage());
            // Don't throw exception to avoid breaking deposit process
        }
    }

    /**
     * Send deposit notification email asynchronously if user email is verified
     */
    @Async
    protected void sendDepositNotificationEmailAsync(String tenantId, GTransaction transaction, GUser user, PaymentMethod paymentMethod) {
        try {
            // Check if user email is verified
            if (Boolean.FALSE.equals(user.getVerified())) {
                log.debug("Skipping deposit email notification for user {} - email not verified", user.getId());
                return;
            }

            // Prepare notification data
            String transactionId = transaction.getExternalTransactionId() != null ? transaction.getExternalTransactionId() : transaction.getId().toString();
            String amount = transaction.getOriginalAmount() != null ? transaction.getOriginalAmount().toString() : transaction.getChange().toString();
            String currency = user.getPreferredCurrency() != null ? user.getPreferredCurrency().getCode() : "USD";
            String method = paymentMethod != null ? paymentMethod.getName() : transaction.getSource();
            String depositTime = CommonHelper.formatTimeToNotification(transaction.getCreatedAt(), user.getUserZone());

            // Get tenant information
            final Tenant tenant = tenantService.findByTenantId(tenantId).orElseThrow(()
                    -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

            // Prepare template variables
            Map<String, String> variables = new HashMap<>();
            variables.put("username", user.getUserName());
            variables.put("email", user.getEmail());
            variables.put("amount", amount);
            variables.put("currency", "USD");
            variables.put("method", method);
            variables.put("transaction_id", transactionId);
            variables.put("deposit_time", depositTime);
            variables.put("domain", tenant.getDomain());
            variables.put("support_email", "support@" + tenant.getDomain());

            // Send email using template
            emailService.sendTemplateEmail(tenantId, user.getEmail(), TemplateTypes.Email.DEPOSIT_NOTIFICATION, variables);

            log.info("Deposit notification email sent successfully to: {}", user.getEmail());
        } catch (Exception e) {
            log.error("Failed to send deposit notification email for user {}: {}", user.getId(), e.getMessage());
            // Don't throw exception to avoid breaking deposit process
        }
    }

}
