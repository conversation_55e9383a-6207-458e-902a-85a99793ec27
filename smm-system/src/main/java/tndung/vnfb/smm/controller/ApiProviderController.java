package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.ApiProviderEditReq;
import tndung.vnfb.smm.dto.request.ApiProviderReq;
import tndung.vnfb.smm.dto.request.OnCreate;
import tndung.vnfb.smm.dto.response.ApiProviderRes;
import tndung.vnfb.smm.service.ApiProviderService;

import javax.validation.groups.Default;
import java.math.BigDecimal;
import java.util.List;



@RestController
@RequestMapping("/v1/providers")
@RequiredArgsConstructor
public class ApiProviderController {

    private final ApiProviderService apiProviderService;

    @PostMapping()
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<ApiProviderRes> add(@RequestBody @Validated(OnCreate.class) ApiProviderReq apiProviderReq) {
        return ApiResponseEntity.success(apiProviderService.add(apiProviderReq));
    }

    @GetMapping()
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<ApiProviderRes>> getAll() {
        return ApiResponseEntity.success(apiProviderService.getAllByTenant());
    }

    @GetMapping("/{id}/detail")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<ApiProviderRes> getById(@PathVariable Long id) {
        return ApiResponseEntity.success(apiProviderService.getById(id));
    }
    @PutMapping("/{id}/edit")
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<ApiProviderRes> edit(@PathVariable Long id, @RequestBody  @Validated(Default.class) ApiProviderEditReq req) {
        return ApiResponseEntity.success(apiProviderService.edit(id ,req));
    }



    @PutMapping("/balance")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> checkAllBalance() {
        apiProviderService.checkAllBalance();
        return ApiResponseEntity.success();
    }

    @PutMapping("/{id}/balance")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<ApiProviderRes> checkBalance(@PathVariable Long id) {
        return ApiResponseEntity.success(apiProviderService.checkBalance(id));
    }

    @PutMapping("/{id}/active")
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<ApiProviderRes> active(@PathVariable Long id) {
        return ApiResponseEntity.success(apiProviderService.active(id));
    }

    @PutMapping("/{id}/deactivate")
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<ApiProviderRes> deactivate(@PathVariable Long id) {
        return ApiResponseEntity.success(apiProviderService.deactivate(id));
    }

    @PatchMapping("/{id}/api-key")
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> editApiKey(@PathVariable Long id, @RequestParam(name = "api-key")  String apiKey) {
        apiProviderService.changeApiKey(id ,apiKey);
        return ApiResponseEntity.success();
    }
    @PatchMapping("/{id}/balance-alert")
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> editBalanceAlert(@PathVariable Long id, @RequestParam(name = "balance-alert") BigDecimal balanceAlert) {
        apiProviderService.changeBalanceAlert(id ,balanceAlert);
        return ApiResponseEntity.success();
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> delete(@PathVariable Long id) {
        apiProviderService.delete(id);
        return ApiResponseEntity.success();
    }
}
