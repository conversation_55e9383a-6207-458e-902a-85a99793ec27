package tndung.vnfb.smm.controller;

import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.DripfeedReq;
import tndung.vnfb.smm.dto.response.DripfeedRes;
import tndung.vnfb.smm.entity.Dripfeed;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.service.DripfeedService;
import tndung.vnfb.smm.service.DripfeedProcessingService;
import tndung.vnfb.smm.service.DripfeedMonitoringService;
import tndung.vnfb.smm.service.AuthenticationFacade;

import java.util.Map;

/**
 * REST controller for dripfeed operations
 */
@RestController
@RequestMapping("/v1/dripfeeds")
@RequiredArgsConstructor
@Slf4j
public class DripfeedController {

    private final DripfeedService dripfeedService;
    private final DripfeedProcessingService dripfeedProcessingService;
    private final DripfeedMonitoringService dripfeedMonitoringService;
    private final AuthenticationFacade authenticationFacade;

    @PostMapping
    public ApiResponseEntity<DripfeedRes> createDripfeed(@Valid @RequestBody DripfeedReq request) {
        Long userId = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND))
                .getId();
        DripfeedRes response = dripfeedService.createDripfeed(request, userId);

        // If schedule_time is null, process immediately
        if (request.getScheduleTime() == null) {
            log.info("Schedule time is null for dripfeed {}, processing immediately", response.getId());
            try {
                // Process the dripfeed immediately in a separate thread to avoid blocking the response
                dripfeedProcessingService.processSpecificDripfeed(response.getId());
            } catch (Exception e) {
                log.error("Error processing immediate dripfeed {}: {}", response.getId(), e.getMessage(), e);
                // Don't fail the creation, just log the error - the scheduler will pick it up later
            }
        }

        return ApiResponseEntity.success(response);
    }

    @GetMapping("/me")
    public ApiResponseEntity<Page<DripfeedRes>> getUserDripfeeds(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Long userId = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND))
                .getId();
        Pageable pageable = PageRequest.of(page, size);
        Page<DripfeedRes> response = dripfeedService.getUserDripfeeds(userId, pageable);
        return ApiResponseEntity.success(response);
    }

    @GetMapping("/me/search")
    public ApiResponseEntity<Page<DripfeedRes>> searchUserDripfeeds(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status) {

        Long userId = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND))
                .getId();
        Pageable pageable = PageRequest.of(page, size);
        Page<DripfeedRes> response = dripfeedService.searchUserDripfeeds(userId, keyword, status, pageable);
        return ApiResponseEntity.success(response);
    }

    @GetMapping("/{id}")
    public ApiResponseEntity<DripfeedRes> getDripfeedById(@PathVariable Long id) {
        Long userId = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND))
                .getId();
        DripfeedRes response = dripfeedService.getDripfeedById(id, userId);
        return ApiResponseEntity.success(response);
    }

    @PutMapping("/{id}/status")
    public ApiResponseEntity<DripfeedRes> updateDripfeedStatus(
            @PathVariable Long id,
            @RequestParam Dripfeed.DripfeedStatus status) {

        DripfeedRes response = dripfeedService.updateDripfeedStatus(id, status);
        return ApiResponseEntity.success(response);
    }

    @DeleteMapping("/{id}")
    public ApiResponseEntity<Void> deleteDripfeed(@PathVariable Long id) {
        Long userId = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND))
                .getId();
        dripfeedService.deleteDripfeed(id, userId);
        return ApiResponseEntity.success(null);
    }

    @PostMapping("/{id}/stop")
    public ApiResponseEntity<DripfeedRes> stopDripfeed(@PathVariable Long id) {
        Long userId = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND))
                .getId();
        DripfeedRes response = dripfeedService.stopDripfeedByUser(id, userId);
        return ApiResponseEntity.success(response);
    }

    // Admin endpoints
    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL') or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<Page<DripfeedRes>> getAllDripfeeds(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<DripfeedRes> response = dripfeedService.getAllDripfeeds(pageable);
        return ApiResponseEntity.success(response);
    }

    @GetMapping("/admin/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<Page<DripfeedRes>> searchAllDripfeeds(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(name = "user_id", required = false) Long userId) {

        Pageable pageable = PageRequest.of(page, size);
        Page<DripfeedRes> response = dripfeedService.searchAllDripfeeds(keyword, status, userId, pageable);
        return ApiResponseEntity.success(response);
    }

    @GetMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<DripfeedRes> getAdminDripfeedById(@PathVariable Long id) {
        DripfeedRes response = dripfeedService.getDripfeedById(id, null); // null userId for admin access
        return ApiResponseEntity.success(response);
    }

    @DeleteMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<Void> deleteAdminDripfeed(@PathVariable Long id) {
        dripfeedService.deleteDripfeed(id, null); // null userId for admin access
        return ApiResponseEntity.success(null);
    }

    // Processing endpoints for admin
    @PostMapping("/admin/process/ready")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<String> processReadyDripfeeds() {
        try {
            dripfeedProcessingService.processReadyDripfeeds();
            return ApiResponseEntity.success("Ready dripfeeds processing triggered successfully");
        } catch (Exception e) {
            log.error("Error processing ready dripfeeds manually", e);
            return ApiResponseEntity.failure(HttpStatus.INTERNAL_SERVER_ERROR, "Error processing ready dripfeeds: " + e.getMessage(), null);
        }
    }

    @PostMapping("/admin/process/loops")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<String> processRunningLoopDripfeeds() {
        try {
            dripfeedProcessingService.processRunningLoopDripfeeds();
            return ApiResponseEntity.success("Running loop dripfeeds processing triggered successfully");
        } catch (Exception e) {
            log.error("Error processing running loop dripfeeds manually", e);
            return ApiResponseEntity.failure(HttpStatus.INTERNAL_SERVER_ERROR, "Error processing running loop dripfeeds: " + e.getMessage(), null);
        }
    }

    @GetMapping("/admin/health")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<DripfeedMonitoringService.DripfeedHealthReport> getDripfeedHealth() {
        try {
            DripfeedMonitoringService.DripfeedHealthReport healthReport = dripfeedMonitoringService.getHealthReport();
            return ApiResponseEntity.success(healthReport);
        } catch (Exception e) {
            log.error("Error getting dripfeed health report", e);
            return ApiResponseEntity.failure(HttpStatus.INTERNAL_SERVER_ERROR, "Error getting dripfeed health report: " + e.getMessage(), null);
        }
    }

    @GetMapping("/admin/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<Map<String, Long>> getDripfeedStatistics() {
        try {
            Map<String, Long> statistics = dripfeedMonitoringService.getDripfeedStatistics();
            return ApiResponseEntity.success(statistics);
        } catch (Exception e) {
            log.error("Error getting dripfeed statistics", e);
            return ApiResponseEntity.failure(HttpStatus.INTERNAL_SERVER_ERROR, "Error getting dripfeed statistics: " + e.getMessage(), null);
        }
    }

    @PostMapping("/admin/cleanup/stuck")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<String> handleStuckDripfeeds() {
        try {
            int handledCount = dripfeedMonitoringService.handleStuckDripfeeds();
            return ApiResponseEntity.success("Handled " + handledCount + " stuck dripfeeds");
        } catch (Exception e) {
            log.error("Error handling stuck dripfeeds", e);
            return ApiResponseEntity.failure(HttpStatus.INTERNAL_SERVER_ERROR, "Error handling stuck dripfeeds: " + e.getMessage(), null);
        }
    }

    @PostMapping("/admin/{id}/stop")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<DripfeedRes> stopDripfeedByAdmin(@PathVariable Long id) {
        try {
            Long adminUserId = authenticationFacade.getCurrentUser()
                    .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND))
                    .getId();

            DripfeedRes stoppedDripfeed = dripfeedService.stopDripfeedByAdmin(id, adminUserId);
            return ApiResponseEntity.success(stoppedDripfeed);
        } catch (Exception e) {
            log.error("Error stopping dripfeed {} by admin", id, e);
            return ApiResponseEntity.failure(HttpStatus.INTERNAL_SERVER_ERROR, "Error stopping dripfeed: " + e.getMessage(), null);
        }
    }

    @PostMapping("/admin/cleanup/old")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PANEL')  or hasRole('CHILD_PANEL')")
    public ApiResponseEntity<String> cleanupOldDripfeeds(@RequestParam(defaultValue = "30") int daysOld) {
        try {
            int cleanedCount = dripfeedMonitoringService.cleanupOldCompletedDripfeeds(daysOld);
            return ApiResponseEntity.success("Cleaned up " + cleanedCount + " old completed dripfeeds");
        } catch (Exception e) {
            log.error("Error cleaning up old dripfeeds", e);
            return ApiResponseEntity.failure(HttpStatus.INTERNAL_SERVER_ERROR, "Error cleaning up old dripfeeds: " + e.getMessage(), null);
        }
    }


}
