{"English": "English", "Language Settings": "Language Settings", "Default Language": "Default Language", "Choose the default language for new users and visitors who haven't logged in yet.": "Choose the default language for new users and visitors who haven't logged in yet.", "Default Language Settings": "Default Language Settings", "Save Changes": "Save Changes", "login": "<PERSON><PERSON>", "email": "Email", "password": "Password", "forgot_password": "Forgot password?", "no_account": "Don't have an account?", "register_now": "Register now", "search": "Search", "all_orders": "All orders", "serial_number": "Serial Number", "user": "User", "link": "Link", "quantity": "Quantity", "total_amount": "Total Amount", "cash_flow": "Cash Flow", "description": "Description", "pagination_info": "Showing 1 to 8 of 50 entries", "show_entries": "Show 8", "z_input_message": "z-input works!", "service": "Service", "grocery": "Grocery", "resources": "Resources", "agency": "Agency", "selected": "selected", "account_status": "Account Status", "new_user": "New User", "find_out_reason": "Do you want to find out the reason?", "note": "Note", "bank_account": "Bank Account", "want_to_know_more": "Do you want to know more?", "conversion_rate": "Conversion Rate", "total_spent": "Total Spent", "select_platform": "Select Platform", "all_networks": "All Networks", "facebook": "Facebook", "instagram": "Instagram", "youtube": "YouTube", "new_order": "New Order", "auto_subscription": "auto subscription", "classification": "Classification", "order_limit": "Order Limit", "average_time": "Average Time", "charge_fee": "Charge Fee", "send": "Send", "example": "Example:", "start_time": "Start Time", "immediately": "Immediately", "guarantee": "Guarantee", "refund_period": "Refund Period", "speed": "Speed", "growth_rate": "Growth Rate", "processing_time": "Processing Time", "placeholder_text": "Lorem ipsum dolor sit amet consectetur...", "mass_order": "Mass Order", "simple_order": "Simple Order", "classic_order": "Classic Order", "content": "Content", "price": "Price", "create_order": "Create Order", "list": "List", "category_required": "Please select a category", "service_required": "Please select a service", "content_required": "Please enter content", "content_min_length": "Content must be at least 10 characters", "quantity_required": "Please enter quantity", "quantity_min": "Minimum quantity is", "quantity_max": "Maximum quantity is", "create_website": "Create custom website", "domain_name": "Domain Name", "exchange_rate": "Exchange Rate", "admin_name": "Admin Name", "admin_password": "Admin Password", "confirm_admin_password": "Confirm Admin Password", "retail_price": "Retail Price", "payment": "Payment", "domain_setup_guide": "If you have a domain, you just need to change the name server...", "search_orders": "Search Orders", "go_button": "Go", "copy_id": "Copy ID", "refill": "Refill", "actions": "Actions", "remaining_balance": "Remaining", "status": "Status", "new_announcement": "New Announcement", "size_cm": "Size (cm):", "bulk_order_support": "For bulk orders, contact admin for pricing support:", "here": "Here", "close": "Close", "no_more_display": "Viewed", "order_success": "Order placed successfully", "link_details": "Link details:", "confirm": "Confirm", "popup_title": "Popup Title", "modal_description": "This is a centered modal with a full-screen overlay.", "products_working": "products works!", "gmail": "Gmail", "information": "Information", "buy_now": "Buy Now", "platform": "Platform", "category": "Category", "min_value": "Min", "max_value": "Max", "api_info": "API Information", "action": "Action", "order": "Order", "order_diary": "Order Diary", "cash_flow_diary": "Cash Flow Diary", "deposit_money": "Deposit Money", "support": "Support", "api": "API", "answer_question": "Answer Question", "earn_money": "<PERSON><PERSON><PERSON>", "online_status": "Online", "view_all_stalls": "View All Stalls", "edit": "Edit", "introduction": "Introduction", "account": "Account", "full_name": "Full Name", "registration_date": "Registration Date", "balance": "Balance", "stall_count": "Stall Count", "products_bought": "Bought", "bought_count": "Products Bought Count", "products_sold": "Sold", "sold_count": "Products Sold Count", "article_count": "Article Count", "no_articles": "No Articles", "buy_via_api": "Buy via API", "api_token": "Token: XXXXXX", "time": {"hours": "hours", "minutes": "minutes", "seconds": "seconds"}, "favorite": "Favorite", "favorite_services": "Favorite Services", "no_favorite_services": "No favorite services", "no_services_available": "No services available", "select_platform_first": "Please select platform first", "comments": "Comments", "enter_comments_one_per_line": "Enter comments, one per line", "enter_links_one_per_line": "Enter each link on a new line", "voucher_code": "Voucher Code", "enter_voucher_code": "Enter voucher code", "apply": "Apply", "clear": "Clear", "voucher_applied": "Voucher code has been applied", "invalid_voucher": "Invalid voucher code", "voucher_expired": "Voucher code has expired", "voucher_limit_reached": "Voucher code has reached usage limit", "processing": "Processing...", "order_results": "Order Results", "success": "Success", "failed": "Failed", "success_status": "✓ Success", "per_day": "per day", "failed_status": "✗ Failed", "format_hint": "Format", "one_order_per_line": "One order per line", "links_count": "Links Count", "total_price": "Total Price", "profile": {"information": "Information", "email": "Email", "phone_number": "Phone Number", "current_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm Password", "update": "Update", "updating": "Updating...", "update_password": "Update Password", "updating_password": "Updating password...", "change_password": "Change Password", "verify": "Verify", "verifying": "Verifying...", "already_verified": "Already verified", "email_required": "Email is required", "email_invalid": "Invalid email", "phone_required": "Phone number is required", "current_password_required": "Current password is required", "password_min_length": "Password must be at least 8 characters", "passwords_not_match": "Passwords do not match", "password_updated": "Password updated successfully", "password_error": "Unable to update password", "profile_updated": "Profile updated successfully", "profile_error": "Unable to update profile", "settings": "Settings", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "appearance": "Appearance", "timezone": "Timezone", "login_history": "Login History", "login_history_tab": "Login History", "loading_history": "Loading history...", "no_login_history": "No login history", "time": "Time", "ip": "IP Address", "device": "<PERSON><PERSON>", "login_records": "login records", "security": "Security", "two_factor_auth": "Two-Factor Authentication", "two_factor_auth_description": "Add an extra layer of security to your account by requiring a verification code in addition to your password.", "enable": "Enable", "disable": "Disable", "enabled": "Enabled", "disabled": "Disabled", "api_key": "API Key", "api_key_description": "Use this key to access our API services.", "generate": "Generate", "regenerate": "Regenerate", "copy": "Copy", "copied": "Copied!", "never_share": "Never share your API key with anyone.", "last_generated": "Last Generated", "account": "Account", "online": "Online", "balance": "Balance", "orders": "Orders", "status": "Status", "successful": "Successful", "page": "Page", "of": "of"}, "each_line_is_one_comment": "Each line corresponds to one comment", "current_comments_count": "Current comments count", "quantity_based_on_comments": "Quantity based on comments", "transaction_types": {"order": "Order", "spent": "Spent", "bonus": "Bonus", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "refund": "Refund", "all": "All transaction types"}, "no_transactions": "No transactions", "favorites": {"no_favorites": "No favorite services found", "min_value": "Minimum Value", "max_value": "Maximum Value", "exchange_rate": "Exchange Rate", "average_time": "Average Time"}, "min_max": "Limits", "filter": {"all_platforms": "All Platforms", "all_categories": "All Categories", "all_services": "All Services", "all_providers": "All Providers", "button": "Filter"}, "reset": "Reset", "date_range": "Date Range", "transaction_type": "Transaction Type", "order_id": "Order ID", "all_transactions": "All Transactions", "guard_test": "Guard Test", "guard_test_description": "This page is protected by newOrderGuard. If you see this page, you have successfully logged in.", "error": {"not_found_title": "Page Not Found", "not_found_message": "The page you are looking for does not exist or has been moved.", "unauthorized_title": "Unauthorized Access", "unauthorized_message": "You need to log in to access this page.", "forbidden_title": "Access Forbidden", "forbidden_message": "You do not have permission to access this page.", "server_error_title": "Server Error", "server_error_message": "A server error occurred. Please try again later.", "go_back": "Go Back", "go_home": "Go Home", "login": "<PERSON><PERSON>", "refresh": "Refresh Page"}, "i18n_management": {"title": "Language Management", "translations_tab": "Translations", "languages_tab": "Languages", "language_selection": "Language Selection", "select_language": "Select language to edit", "load_translations": "Load Translations", "search_translations": "Search translations", "filter_by_category": "Filter by category", "all_categories": "All Categories", "translation_key": "Translation Key", "translation_value": "Translation Value", "original_value": "Original Value", "modified": "Modified", "save_translations": "Save Translations", "saving": "Saving...", "download_translations": "Download Translations", "upload_translations": "Upload Translations", "upload_file": "Upload File", "choose_file": "Choose <PERSON>", "file_format": "File Format: JSON", "upload_success": "Upload successful", "upload_error": "Upload error", "invalid_json": "Invalid JSON file", "download_success": "Download successful", "no_changes": "No changes to save", "unsaved_changes": "Unsaved changes", "confirm_discard": "Are you sure you want to discard changes?", "statistics": "Statistics", "total_keys": "Total Keys", "customized_keys": "Customized Keys", "last_modified": "Last Modified", "language_management": "Language Management", "predefined_languages": "Predefined Languages", "custom_languages": "Custom Languages", "add_custom_language": "Add Custom Language", "edit_language": "Edit Language", "language_code": "Language Code", "language_name": "Language Name", "flag_class": "Flag Class", "description": "Description", "active": "Active", "create": "Create", "update": "Update", "delete": "Delete", "cancel": "Cancel", "confirm_delete": "Are you sure you want to delete this language?", "delete_success": "Delete successful", "delete_error": "Delete error", "create_success": "Create successful", "create_error": "Create error", "update_success": "Update successful", "update_error": "Update error", "save_success": "Save successful", "save_error": "Save error", "load_error": "Load error", "default": "<PERSON><PERSON><PERSON>", "set_default": "Set as <PERSON><PERSON><PERSON>", "selected_languages": "Selected Languages", "available_languages": "Available Languages", "no_languages_selected": "No languages selected", "loading": "Loading...", "processing": "Processing..."}, "email_templates": {"description": "Manage email notification templates for different events", "login_notification": "Login Notification", "deposit_notification": "Deposit Notification", "order_notification": "Order Notification", "ticket_notification": "Ticket Notification", "password_reset": "Password Reset", "subject": "Subject", "content": "Content", "enter_subject": "Enter email subject...", "enter_content": "Enter email content...", "available_variables": "Available Variables", "click_to_insert": "Click to insert variable"}, "Total Keys": "Total Keys", "Customized": "Customized", "Unsaved Changes": "Unsaved Changes", "No languages available for editing": "No languages available for editing", "Configure languages in Language Management & Settings tab first": "Configure languages in Language Management & Settings tab first", "Search translations...": "Search translations...", "All Categories": "All Categories", "Reset": "Reset", "Loading translations...": "Loading translations...", "Translation Key": "Translation Key", "Translation Value": "Translation Value", "Status": "Status", "Enter translation...": "Enter translation...", "Modified": "Modified", "Saved": "Saved", "No translations found": "No translations found", "Try adjusting your search or filter criteria": "Try adjusting your search or filter criteria", "Showing": "Showing", "translations": "translations", "Last modified:": "Last modified:", "Reset All Changes": "Reset All Changes", "Save All Changes": "Save All Changes", "Predefined Languages": "Predefined Languages", "Choose from our extensive list of predefined languages": "Choose from our extensive list of predefined languages", "Search predefined languages...": "Search predefined languages...", "Default": "<PERSON><PERSON><PERSON>", "Set Default": "<PERSON>", "Custom Languages": "Custom Languages", "Create your own custom languages (e.g., \"Vietnam 2\", \"English US\")": "Create your own custom languages (e.g., \"Vietnam 2\", \"English US\")", "Add Custom Language": "Add Custom Language", "No custom languages": "No custom languages", "Create your first custom language to get started": "Create your first custom language to get started", "Edit Custom Language": "Edit Custom Language", "Language Code": "Language Code", "Use lowercase letters, numbers, underscores, and hyphens only": "Use lowercase letters, numbers, underscores, and hyphens only", "Language Name": "Language Name", "Flag Class": "Flag Class", "CSS class for flag icon (optional)": "CSS class for flag icon (optional)", "Description": "Description", "Optional description for this custom language": "Optional description for this custom language", "Active": "Active", "Save Language Configuration": "Save Language Configuration", "Create": "Create", "Update": "Update", "My currencies": "My Currencies", "Configure available currencies for your users": "Configure available currencies for your users", "Main currency": "Main Currency", "Secondary currencies": "Secondary Currencies", "No secondary currencies selected": "No secondary currencies selected", "Click \"List of currencies\" to add more": "Click \"List of currencies\" to add more", "List of currencies": "List of Currencies", "Selecting the secondary currencies": "Selecting the Secondary Currencies", "Choose additional currencies for your users": "Choose additional currencies for your users", "Search for currency": "Search for currency", "currencies selected": "currencies selected", "Apply": "Apply", "Cancel": "Cancel", "Save": "Save", "Saving...": "Saving...", "No currencies found": "No currencies found", "Loading currencies...": "Loading currencies...", "Currency settings updated successfully": "Currency settings updated successfully", "Failed to update currency settings": "Failed to update currency settings", "Failed to load currencies": "Failed to load currencies", "Remove currency": "Remove currency", "header": {"main_menu": "Main Menu", "users": "Users", "orders": "Orders", "services": "Services", "add-fund": "Add Fund", "support": "Support", "statistics": "Statistics", "more": "More", "dashboard": "Dashboard", "settings": "Settings", "dripfeed": "Dripfeed", "profile": "Profile", "logout": "Logout", "copyright": "© 2024 AutoSMM", "version": "Version 1.0.0", "current_balance": "Current Balance", "panel_pricing": "Panel Pricing", "per_month": "per month", "affordable_subscription": "Affordable subscription plan", "add_funds": "Add Funds", "history": "History", "how_commission_works": "How does commission work?"}, "admin": {"invalid_domain_name": "Invalid domain name", "main_menu": "Main Menu", "child_panels": {"title": "Child Panels Management", "description": "Manage child panels for current tenant", "search_placeholder": "Search by domain, email, or status...", "search": "Search", "reset": "Reset", "loading": "Loading child panels...", "domain": "Domain", "status": "Status", "contact_email": "Contact Email", "subscription_start_date": "Start Date", "subscription_end_date": "End Date", "days_until_expiration": "Days Until Expiration", "created_at": "Created At", "credentials": "Credentials", "actions": "Actions", "no_data": "No child panels found", "no_data_description": "No child panels have been created for this tenant yet.", "show_credentials": "Show Credentials", "promote_to_parent": "Promote to Parent", "move_to_parent": "Move to Parent", "delete_child_panel": "Delete Child Panel", "total_child_panels": "Total Child Panels", "current_tenant": "Current Tenant"}, "nav": {"add_fund": "Add Fund", "users": "Users", "orders": "Orders", "dripfeed": "Dripfeed", "services": "Services", "support": "Support", "statistics": "Statistics", "more": "More", "dashboard": "Dashboard", "settings": "Settings"}, "dripfeed": {"title": "Dripfeed Management", "search_placeholder": "Search by user, service, or link...", "user": "User", "service": "Service", "quantity": "Quantity", "schedule": "Schedule", "loop_settings": "Loop Settings", "status": "Status", "reason": "Reason", "orders": "Orders", "actions": "Actions", "user_id": "ID", "service_id": "ID", "target_link": "Target Link", "created": "Created", "orders_text": "orders", "minutes_spacing": "min spacing", "wait_completion": "Wait completion", "no_wait": "No wait", "single_order": "Single order", "total_orders": "Total", "no_orders_yet": "No orders yet", "start": "Start", "stop": "Stop", "delete": "Delete", "previous": "Previous", "next": "Next", "showing": "Showing", "to": "to", "of": "of", "results": "results", "no_dripfeeds": "No dripfeeds found", "no_dripfeeds_description": "No users have created any scheduled orders yet."}, "statistics": "Statistics", "more": "More", "dashboard": "Dashboard", "settings": "Settings", "copyright": "© 2024 SMM Panel. All rights reserved.", "version": "Version 1.0.0", "admin_panel_management": "Admin Panel Management", "manage_panels_and_users": "Manage all panels and main tenant users", "go_to_panel": "Go to Panel", "panel_management": "Panel Management", "user_management": "User Management", "order_tracking_tab": "Order Tracking", "messages": "Messages", "panels_short": "Panels", "users_short": "Users", "orders_short": "Orders", "messages_short": "Messages", "loading": "Loading...", "support_chat_rooms": "Support Chat Rooms", "chat_conversations_with_users": "Chat conversations with panel users", "chat_room": "Chat Room", "chat_short": "Cha<PERSON>", "no_chat_rooms_yet": "No chat rooms yet", "support_chat_rooms_will_appear_here": "Support chat rooms from panel users will appear here.", "loading_chat_rooms": "Loading chat rooms...", "refresh_chat_rooms": "Refresh Chat Rooms", "failed_to_load_support_chat_rooms": "Failed to load support chat rooms", "unknown_user": "Unknown User", "no_messages_yet": "No messages yet", "search_by_domain": "Search by domain...", "reset": "Reset", "domain": "Domain", "owner": "Owner", "status": "Status", "subscription": "Subscription", "auto_renewal": "Auto Renewal", "created": "Created", "actions": "Actions", "enabled": "Enabled", "disabled": "Disabled", "expires": "Expires", "days_remaining": "days remaining", "no_panels_found": "No panels found", "previous": "Previous", "next": "Next", "showing": "Showing", "to": "to", "of": "of", "results": "results", "search_by_username_email_phone": "Search by username, email, or phone...", "user": "User", "email": "Email", "balance": "Balance", "total_spent": "Total Spent", "last_login": "Last Login", "id": "ID", "never": "Never", "no_users_found": "No users found", "try_again": "Try Again", "no_providers": "No providers", "test_websocket_connection": "Test WebSocket Connection", "ws_test": "WS Test", "force_reconnect_websocket": "Force Reconnect WebSocket", "reconnect": "Reconnect", "test_new_message_indicator": "Test New Message Indicator", "test_indicator": "Test Indicator", "test_connection_stability": "Test Connection Stability", "stability_test": "Stability Test", "loading_messages": "Loading messages...", "start_conversation_by_sending_reply": "Start the conversation by sending a reply.", "new_message": "new message", "s": "s", "type_your_reply": "Type your reply...", "please_enter_valid_days": "Please enter a valid number of days", "subscription_extended_by_days": "Subscription extended by {{days}} days", "failed_to_extend_subscription": "Failed to extend subscription", "admin_panel": {"extend_subscription": "Extend Subscription", "disable_tenant": "Disable Tenant", "enable_tenant": "Enable Tenant", "delete_tenant": "Delete Tenant", "tenant_domain": "Tenant Domain", "extension_days": "Extension Days", "enter_days": "Enter number of days", "extension_days_help": "Enter the number of days to extend the subscription (1-365 days)", "note_optional": "Note (Optional)", "extension_note_placeholder": "Enter a note for this extension...", "extend_subscription_success": "Subscription extended successfully", "disable_tenant_success": "Tenant disabled successfully", "enable_tenant_success": "Tenant enabled successfully", "delete_tenant_success": "Tenant deleted successfully", "extend_subscription_error": "Failed to extend subscription", "disable_tenant_error": "Failed to disable tenant", "enable_tenant_error": "Failed to enable tenant", "delete_tenant_error": "Failed to delete tenant", "confirm_disable": "Are you sure you want to disable this tenant?", "confirm_delete": "Are you sure you want to permanently delete this tenant? This action cannot be undone.", "tenant_management": "Tenant Management"}, "domain_info": {"title": "Domain Information", "expiry_date": "Expiry Date", "days_remaining": "Days Remaining", "total_users": "Total Users", "total_services": "Total Services", "total_orders": "Total Orders", "providers": "Providers", "revenue": "Revenue", "close": "Close", "expired": "Expired", "active": "Active"}, "order_tracking": {"title": "Order Tracking", "all_orders_from_tenants": "All orders from tenants", "filter_by_domain": "Filter by domain", "filter_by_date": "Filter by date", "order_id": "Order ID", "domain": "Domain", "user": "User", "service": "Service", "amount": "Amount", "status": "Status", "created_date": "Created Date", "search_placeholder": "Search by order ID, user, or service...", "no_orders_found": "No orders found", "loading": "Loading...", "from_date": "From Date", "to_date": "To Date", "start_date": "Start Date", "end_date": "End Date", "apply_filter": "Apply Filter", "clear_filter": "Clear Filter", "all_domains": "All Domains", "all_statuses": "All Statuses", "status_pending": "Pending", "status_in_progress": "In Progress", "status_completed": "Completed", "status_partial": "Partial", "status_canceled": "Canceled", "status_canceled_no_refund": "Canceled (No Refund)", "status_failed": "Failed", "apply_filters_first": "Please apply filters to view orders", "apply_filters_description": "Select filter criteria and click 'Apply Filter' to display the order list."}, "users": {"no_referrals": "No referrals found", "referrals_count": "referrals", "title": "User Management", "user_management": "User Management", "search_by_username": "Search by username", "id": "ID", "user": "User", "email": "Email", "balance": "Balance", "total_spend": "Total Spend", "discount_prices": "Discounts & Prices", "referrals": "Referrals", "status": "Status", "last_login": "Last Login", "actions": "Actions", "select_all": "Select All", "selected": "Selected", "custom_discount": "custom discount", "special_prices": "special prices", "referral_rate": "referral rate", "default_rate": "Default Rate", "activated": "Activated", "deactivated": "Deactivated", "total_spend_label": "Total Spend:", "showing": "Showing", "to": "to", "of": "of", "results": "results", "previous": "Previous", "next": "Next", "no_users_found": "No users found matching your criteria.", "reset_search": "Reset Search"}, "services": {"title": "Service Management", "add_service": "Add Service", "import_services": "Import Services", "search": "Search", "no_services": "No services in this category", "no_data": "No service data", "service_name": "Service Name", "min_max": "Min-<PERSON>", "price": "Price", "status": "Status", "actions": "Actions", "configure": "Configure", "drop_service_here": "Drop service here", "no_provider": "No provider", "no_refill": "No refill", "days": "days"}, "orders": {"all_orders": "All Orders", "api_service_id": "API Service", "search_orders": "Search Orders", "search_placeholder": "Search by link or order ID...", "hide_filters": "Hide Filters", "show_filters": "Show Filters", "category": "Category", "service": "Service", "date": "Date", "apply": "Apply", "reset_filter": "Reset Filter", "user_id": "User ID", "enter_user_id": "Enter User ID", "serial_number": "Serial Number", "user": "User", "link": "Link", "remaining": "Remaining", "status": "Status", "actions": "Actions", "amount": "Amount", "quantity": "Quantity", "started": "Started", "remaining_quantity": "Remaining Quantity", "api_order_id": "API Order ID", "provider_url": "Provider URL", "customer_note": "Customer Note", "copy_to_clipboard": "Copy to clipboard", "copy": "Copy", "manual": "Manual"}, "support": {"title": "Support", "support": "Support", "search_by_username": "Search by username", "filter": "Filter", "date_range": "Date Range", "reset": "Reset", "apply_filters": "Apply Filters", "id": "ID", "subject": "Subject", "status": "Status", "last_update": "Last Update", "actions": "Actions", "no_tickets": "No support tickets", "user": "User:", "last_update_label": "Last Update:", "page": "Page", "of": "of"}, "support_detail": {"ticket_detail": "Ticket Detail #", "information": "Information", "subject": "Subject", "customer": "Customer", "created_date": "Created Date", "last_updated": "Last Updated", "status": "Status", "chat": "Cha<PERSON>", "enter_message": "Enter message...", "send": "Send", "loading_services": "Loading services..."}}, "common": {"add": "Add", "cancel": "Cancel", "save": "Save", "save_description": "Save description", "saving": "Saving...", "creating": "Creating...", "updating": "Updating...", "uploading": "Uploading...", "no_results_found": "No results found", "update": "Update", "edit": "Edit", "loading": "Loading...", "refresh": "Refresh", "showing": "Showing", "of": "of"}, "popup": {"new_service": {"service_type": "Service Type", "change": "Change", "provider": "Provider", "search_provider": "Search provider...", "service_id": "Service ID", "loading_services": "Loading services...", "enter_search_hint": "Enter number to search exact ID or text to search name...", "select_service": "Select Service", "description": "Description", "service_name": "Service Name", "enter_service_name": "Enter service name", "service_type_label": "Service Type", "category": "Category", "description_label": "Description", "enter_service_description": "Enter service description", "user_friendly_description": "User-friendly description", "speed_per_day": "Speed per day", "speed_hint": "from 1 to ∞", "link_sample": "<PERSON>", "enter_link_sample": "Enter link sample", "start_time": "Start Time", "start_time_placeholder": "Enter start time", "warranty": "Warranty", "warranty_placeholder": "Enter warranty information", "speed": "Speed", "speed_placeholder": "Enter speed information", "service_labels": "Service Labels", "common_labels": "Common Labels", "add_custom_label": "Add Custom Label", "enter_label_text": "Enter label text", "green": "Green", "red": "Red", "options": "Options", "sync_all_settings": "Sync All Settings", "sync_description": "When enabled, all service attributes will be synced with the provider.", "min_max_label": "Min-<PERSON>", "sync_with_provider": "Sync with provider", "min": "Min", "max": "Max", "sync_service_status": "Sync service status with provider", "sync_status_description": "When enabled, service status (active/inactive) will be synced with the provider", "additional_options": "Additional Options", "cancel_button": "<PERSON><PERSON>", "cancel_button_description": "Enable cancel button for this service", "refill": "Refill", "refill_description": "Enable refill for this service", "refill_days_label": "Refill button available for how many days", "refill_days_hint": "from 1 to 365 days", "refill_days_editable_note": "Refill days can be customized even when sync is enabled", "overflow": "Overflow", "overflow_description": "Enable overflow for this service", "price": "Price", "provider_price": "Provider price per 1000 completions", "provider_rate_description": "This price is calculated based on the provider's exchange rate", "current_rate": "Current rate", "extra_price_percentage": "Your extra price in %", "fixed_price": "Your fixed price", "price_on_panel": "Your price on panel"}}, "settings": {"title": "Settings", "design": {"title": "Design & Content", "description": "Customize your website's appearance", "logo_favicon": {"title": "Logo & Favicon", "logo": "Logo", "favicon": "Favicon", "upload": "Upload", "upload_successful": "Upload successful", "copy_url": "Copy URL", "choose_from_library": "Choose from library", "choose_favicon_from_library": "<PERSON><PERSON> from library", "generate_logo": "Generate Logo", "generate_logo_v2": "Generate Logo V2", "logo_generator": "Logo Generator"}, "logo_generator": {"title": "Advanced Logo Generator", "text": "Logo Text", "size": "Font Size", "style": "Style", "font": "Font", "text_color": "Text Color", "background": "Background", "effects": "Effects", "preview": "Preview", "enter_text": "Enter text to generate logo", "download": "Download", "set_as_logo": "Set as <PERSON><PERSON>", "bold_styles": "Bold Styles", "category_basic": "Basic", "category_gradient": "Gradient", "category_neon": "Neon & Glow", "category_3d": "3D Effects", "category_metallic": "Metallic", "category_animation": "Animation", "category_special": "Special", "tips": "Pro Tips", "tip_1": "Use contrasting colors for better readability", "tip_2": "Keep text short for better visual impact", "tip_3": "Try different effects to match your brand style", "tip_4": "Animated effects will be downloaded as GIF files", "download_info": "Download Information", "will_download_gif": "Will download as animated GIF", "will_download_png": "Will download as high-quality PNG", "generating_gif": "Generating GIF...", "generating_logo": "Generating Logo...", "live_preview": "Live CSS Preview", "canvas_preview": "Canvas Preview", "animated": "Animated"}, "logo_generator_v2": {"title": "Logo Generator V2", "subtitle": "Create professional logos with PSD templates", "logo_text": "Logo Text", "logo_text_placeholder": "Enter your logo text", "slogan_text": "Slogan Text", "slogan_text_placeholder": "Enter your slogan", "has_slogan": "With <PERSON><PERSON><PERSON>", "categories": "Categories", "templates": "Templates", "customize_layers": "Customize Layers", "preview": "Preview", "download": "Download", "set_as_logo": "Set as <PERSON><PERSON>", "reset": "Reset"}, "colors": {"title": "Colors", "primary_color": "Primary Color", "custom": "Custom", "cancel": "Cancel", "apply": "Apply", "preview": "Preview", "active_link": "Active Link", "button": "<PERSON><PERSON>", "icon": "Icon", "primary_button": "Primary Button", "primary_elements": "Primary Elements", "link_text": "Link Text", "button_text": "Button Text"}, "header_style": {"title": "Header Style"}, "sidebar_style": {"title": "Sidebar Style"}, "landing_page": {"title": "<PERSON>", "template": "Template", "preview": "Preview", "new_order": "New Order", "services": "Services", "most_comfortable": "Most Comfortable"}, "save_changes": "Save Changes"}, "nav": {"setup": "Setup", "general_settings": "General Settings", "providers": "Providers", "payment_methods": "Payment Methods", "design": "Design", "internationalization": "Internationalization", "currency": "<PERSON><PERSON><PERSON><PERSON>", "integrations": "Integrations", "connections": "Connections", "telegram_templates": "Telegram Templates", "email_templates": "Email Templates", "telegram_notification": "Telegram Notification", "email_notification": "Email Notification", "notifications": "Notifications", "security": "Security", "api": "API", "analytics": "Analytics", "email": "Email", "sms": "SMS", "funds": "Funds", "transactions": "Transactions", "commission": "Commission", "create": "Create", "notify_users": "Notify Users", "promotions": "Promotions", "promo_codes": "Promo Codes", "extra_pages": "Extra Pages", "child_panels": "Child Panels", "other": "Other", "profile_settings": "Profile Settings", "all_panels": "All Panels"}, "connections": {"title": "Connection Settings", "description": "Configure Telegram Bot and SMTP connections for the system", "status": {"enabled": "Enabled", "disabled": "Disabled"}, "telegram": {"title": "Telegram Notification Bot", "enable": "Enable Telegram <PERSON>", "bot_token": "Bot <PERSON>", "chat_id": "Chat ID", "bot_username": "<PERSON><PERSON>", "webhook_secret": "Webhook Secret", "webhook_placeholder": "Leave empty to auto-generate when setting webhook", "api_server": "API Server", "bot_guide": "View bot creation guide", "set_webhook": "<PERSON>", "proxy_help": "Use proxy if Telegram is blocked in Vietnam", "need_help": "Need help?", "detailed_guide": "View detailed Telegram Bot integration guide"}, "smtp": {"title": "SMTP Configuration", "enable": "Enable SMTP", "host": "SMTP Host", "port": "Port", "email": "SMTP Email", "password": "SMTP Password", "password_placeholder": "Enter SMTP password...", "encryption": "Encryption", "test_connection": "Test Connection", "need_help": "Need help?", "detailed_guide": "View detailed SMTP integration guide"}}, "telegram_templates": {"description": "Configure Telegram notification templates for various system events", "manual_order_notification": "Manual Order Notification for Admin", "api_order_notification": "API Order Notification for Admin", "deposit_notification": "Deposit Notification for Admin", "ticket_notification": "Ticket Notification for Admin", "child_panel_request_notification": "Child Panel Request Notification for Admin", "enter_template": "Enter your template here...", "available_variables": "Available variables"}, "telegram_notification": {"title": "Telegram Notification", "description": "Manage Telegram notification templates for the system", "settings": "Telegram Settings", "no_templates": "No templates", "no_templates_desc": "No Telegram notification templates configured yet"}, "email_notification": {"title": "Email Notification", "description": "Manage Email notification templates for the system", "settings": "<PERSON><PERSON>s", "no_templates": "No templates", "no_templates_desc": "No Email notification templates configured yet"}, "template_edit": {"title": "Edit Template", "telegram_desc": "Edit Telegram notification template", "email_desc": "Edit Email notification template", "subject": "Subject", "subject_placeholder": "Enter email subject...", "subject_required": "Subject is required", "content": "Content", "content_placeholder": "Enter template content...", "content_required": "Content is required", "service_selection": "Service Selection", "select_services_placeholder": "Select services for notification...", "service_selection_help": "Select specific services to receive notifications for, or leave empty to receive notifications for all services.", "all_services": "All Services", "all_services_help": "When enabled, notifications will be sent for all services including new ones added in the future.", "enable_template": "Enable template", "available_variables": "Available Variables", "variables_help": "Click on variables to insert into content", "preview": "Preview", "variables": {"domain": "Domain", "username": "Username", "trans_id": "Transaction ID", "service": "Service", "link": "Link", "comment": "Comment", "quantity": "Quantity", "pay": "Payment", "p": "Price", "time": "Time", "method": "Method", "amount": "Amount", "price": "Price", "subject": "Subject", "content": "Content", "status": "Status", "category": "Category", "summary": "Summary", "id": "ID", "device": "<PERSON><PERSON>", "bank": "Bank", "account_number": "Account Number", "account_name": "Account Name", "email": "Email", "login_time": "Login Time", "ip_address": "IP Address", "user_agent": "User Agent", "support_email": "Support Email", "currency": "<PERSON><PERSON><PERSON><PERSON>", "transaction_id": "Transaction ID", "deposit_time": "Deposit Time", "order_id": "Order ID", "service_name": "Service Name", "order_time": "Order Time", "ticket_id": "Ticket ID", "created_time": "Created Time", "reset_link": "Reset Link", "expiry_time": "Expiry Time"}}, "telegram_settings": {"title": "Telegram Bot Settings", "test_connection": "Test Connection"}, "email_settings": {"title": "Email SMTP Settings"}}, "extra_pages": {"title": "Extra Pages", "description": "Manage custom pages for your dashboard", "create_page": "Create New <PERSON>", "edit_page": "Edit Page", "page_title": "Page Title", "page_path": "URL Path", "page_content": "Content", "is_active": "Active", "sort_order": "Sort Order", "actions": "Actions", "edit": "Edit", "delete": "Delete", "toggle_active": "Toggle Active", "save": "Save", "cancel": "Cancel", "confirm_delete": "Are you sure you want to delete this page?", "path_placeholder": "my-custom-page", "path_help": "Only lowercase letters, numbers, and hyphens allowed", "title_placeholder": "Enter page title", "content_placeholder": "Enter page content...", "created_at": "Created", "updated_at": "Updated", "status": "Status", "active": "Active", "inactive": "Inactive", "no_pages": "No pages found", "create_first_page": "Create your first custom page", "path_exists": "This path already exists", "invalid_path": "Invalid path format", "page_created": "Page created successfully", "page_updated": "Page updated successfully", "page_deleted": "Page deleted successfully", "status_updated": "Status updated successfully", "icon_help": "Enter icon name or URL (for custom icons, use cdn/ prefix)", "active_help": "Enable to display this page on website", "create": "Create", "update": "Update"}, "icon_library": {"title": "Icon Library", "search_placeholder": "Search icons...", "loading": "Loading icons...", "bank_icons": "Bank Icons", "general_icons": "General I<PERSON>s", "no_results": "No icons found matching your search"}, "providers": {"title": "Providers", "your_providers": "Your Providers", "change_key": "Change Key", "balance_alert": "<PERSON><PERSON>", "delete": "Delete", "add_provider": "Add Provider", "edit_provider": "Edit Provider", "change_api_key": "Change API Key", "api_url": "API URL", "api_url_placeholder": "https://example.com/api", "api_key": "API Key", "api_key_placeholder": "Enter API key", "currency_rate": "Currency Rate", "currency_rate_description": "Enable custom currency rate (default: 1)", "rate_value": "Rate Value", "update_key": "Update Key", "name": "Provider Name", "name_placeholder": "Enter provider name", "balance_alert_placeholder": "Enter balance alert amount", "this_provider": "this provider"}, "notifications": {"title": "Notifications Management", "description": "Manage notifications that will be displayed to users on your platform.", "popup_notifications": "Popup Notifications", "fixed_notifications": "Fixed Notifications", "add_popup": "Add <PERSON>", "add_fixed": "Add Fixed", "popup_description": "These notifications appear as popups. Multiple popup notifications can be active at the same time.", "fixed_description": "These notifications appear in a fixed position. Only one fixed notification can be active at a time.", "no_popup_notifications": "No popup notifications found. Click \"Add Popup\" to create one.", "no_fixed_notifications": "No fixed notifications found. Click \"Add Fixed\" to create one.", "show_more": "Show more", "show_less": "Show less", "expires": "Expires", "only_for_customers": "Only for customers", "notification": "Notification", "this_notification": "this notification", "dismiss": "<PERSON><PERSON><PERSON>", "close": "Close", "position_selection": {"title": "Select Notification Position", "select_position": "Select Target Page", "home_page": "Home Page", "home_description": "Display notification on the main dashboard page", "add_fund_page": "Add Fund Page", "add_fund_description": "Display notification on the payment/deposit page"}}, "integrations": {"title": "Services integrations", "description": "Connect your platform with external services", "available_integrations": "Available Integrations", "loading_integrations": "Loading integrations...", "connected": "Connected", "not_connected": "Not connected", "edit": "Edit", "connect": "Connect", "disconnect": "Disconnect", "no_integrations": "No integrations available at the moment.", "add_custom": "Add Custom Integration", "custom": {"title": "Add Custom Integration", "description": "Create a custom integration with your own icon and link", "key": "Integration Key", "key_placeholder": "Enter unique key (e.g., discord, youtube)", "key_required": "Integration key is required", "key_pattern": "Key can only contain letters, numbers, hyphens and underscores", "value": "Integration Link", "value_placeholder": "https://example.com/your-link", "value_required": "Integration link is required", "value_pattern": "Please enter a valid URL starting with http:// or https://", "position": "Position", "icon": "Icon", "select_icon": "Select an icon", "upload_icon": "Upload PNG Icon", "uploading": "Uploading...", "create": "Create Integration", "creating": "Creating..."}}, "new_notification": {"create_title": "Create New Notification", "edit_title": "Edit Notification", "new_notification": "New Notification", "new_popup_notification": "New Popup Notification", "new_fixed_notification": "New Fixed Notification", "edit_popup_notification": "Edit Popup Notification", "edit_fixed_notification": "Edit Fixed Notification", "title": "Title", "title_required": "Title is required", "enter_title": "Enter notification title", "content": "Content", "content_required": "Content is required", "enter_content": "Enter notification content", "enter_html_content": "Enter HTML content", "auto_dismiss": "Auto dismiss notification", "auto_dismiss_description": "When enabled, notification will be automatically dismissed after specified hours", "turn_off_after": "Turn off after", "hours": "hours", "notification_expiry": "Notification Expiry", "never": "Never", "never_description": "Notification will not expire automatically", "after_some_time": "After some time", "after_some_time_description": "Notification will expire after a specified time period", "specific_date": "Specific date", "specific_date_description": "Notification will expire on a specific date and time", "expires_on": "Expires on", "date": "Date", "time": "Time", "show_preview": "Show Preview", "hide_preview": "Hide Preview", "preview": "Preview", "create_notification": "Create Notification", "update_notification": "Update Notification", "creating": "Creating...", "updating": "Updating..."}, "promo_codes": {"title": "Promo codes", "create_promo_code": "Create promo code", "code": "Code", "type": "Type", "amount": "Amount", "activations": "Activations", "status": "Status", "created_at": "Created at", "actions": "Actions", "top_up_balance": "Top up balance", "discount_for_order": "Discount for order", "active": "Active", "used": "Used", "no_promo_codes": "No promo codes found. Create your first promo code.", "promo_code_type": "Promo code type", "promo_code": "Promo code", "promo_code_amount": "Promo code amount", "activations_count": "Activations count", "enter_percentage": "Enter percentage", "enter_amount": "Enter amount", "enter_activations": "Enter number of activations", "random": "Random", "creating_promo": "Creating...", "create_promo": "Create promo code", "example_code": "For example, HELLO500", "validation": {"code_required": "Please enter a promo code", "amount_required": "Please enter a valid discount amount", "percentage_max": "Discount percentage cannot exceed 100%", "activations_required": "Please enter a valid activations count", "create_failed": "Failed to create promo code"}}, "panels": {"title": "Panels", "start_business": "Start your business with smart panel.", "price_per_month": "Price: $7.7 per month", "new_panel": "New panel", "creating": "Creating...", "domain": "DOMAIN", "expiry": "EXPIRY", "days_left": "DAYS LEFT", "renew": "RENEW", "status": "STATUS", "actions": "ACTIONS", "restore": "Rest<PERSON>", "auto_renewal": "Auto Renewal", "no_panels": "No panels found", "expired": "Expired", "active": "Active", "days_left_text": "days left", "expired_text": "Expired", "auto_renewal_enabled": "Auto-renewal enabled", "auto_renewal_disabled": "Auto-renewal disabled", "failed_to_load": "Failed to load panels", "failed_to_update_auto_renewal": "Failed to update auto-renewal", "restore_not_implemented": "Restore functionality not implemented yet", "actions_not_implemented": "Actions menu not implemented yet", "expires_today": "Expires today", "day_left": "day left", "unknown": "Unknown", "error": "Error", "new": "New", "configuring": "Configuring", "pending": "Pending", "invalid_date": "Invalid Date", "panel_created_success": "Panel created successfully for domain:", "failed_to_create": "Failed to create panel"}, "general_settings": {"title": "General Settings", "description": "Configure your site settings", "extra_features": "Extra features", "services_settings": "Services settings", "language": "Language", "promotions": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "discount_system": "Discount System ( Rank - Position )", "affiliate_system": "Affiliate system", "top_10_users": "Top-10 users", "convertation": "Convertation", "currency": "<PERSON><PERSON><PERSON><PERSON>", "primary_currency": "Primary currency:", "active": "Active", "inactive": "Inactive", "decimal_places": "Decimal Places", "decimal_places_description": "Display", "decimal_places_help": "Choose the number of decimal places to display for currency values (from 2 to 8)", "decimal_places_unit": "digits", "example": "Example", "close": "Close", "average_time_calculation": "Average Time Calculation", "average_time_description": "Configure average time calculation", "average_time_help": "Configure how to calculate average time based on the latest orders and recalculation interval", "order_count_label": "Calculate based on x latest orders", "hours_label": "Recalculation time (hours)", "orders": "orders", "hours": "hours", "cancel": "Cancel", "save": "Save"}, "auth": {"cancel": "Cancel", "google_authentication_setting": "Google authentication setup", "get_google_authenticator": "Get Google Authenticator on your phone", "get_on_android": "Get on Android", "get_on_ios": "Get on iOS", "scan_qr_code_instruction": "Scan the QR code. It will generate a 6-digit code for you to enter below.", "manual_entry_instruction": "If you have trouble using the QR code, select manual entry in the app and enter username and code:", "enter_authentication_code": "Enter authentication code", "code_required": "Authentication code is required", "code_must_be_6_digits": "Code must be 6 digits", "enable": "Enable", "disable": "Disable", "disable_two_factor_auth": "Disable two-factor authentication", "disable_two_factor_auth_confirmation": "Please enter your password to disable two-factor authentication.", "disable_two_factor_auth_warning": "Warning: Disabling two-factor authentication will make your account less secure. Anyone with your password will be able to access your account.", "register": "Register", "welcome_back": "Welcome back", "sign_in_to_continue": "Sign in to continue", "username": "Username", "enter_username": "Enter username", "username_required": "Username is required", "username_invalid": "Invalid username", "password": "Password", "forgot_password": "Forgot password?", "enter_password": "Enter password", "password_required": "Password is required", "remember_me": "Remember me", "login": "<PERSON><PERSON>", "logging_in": "Logging in...", "no_account": "Don't have an account?", "register_now": "Register now", "create_your_panel": "Create your own SMM panel", "join_thousands": "Join thousands of successful SMM panel owners", "start_your_business": "Start your SMM business today", "mfa_verification": "Two-Factor Authentication", "mfa_instruction": "Enter the 6-digit code from your authenticator app", "mfa_code": "Verification Code", "mfa_code_required": "Verification code is required", "mfa_code_length": "Verification code must be 6 digits", "verify": "Verify", "verifying": "Verifying...", "back_to_login": "Back to login"}, "signup": {"create_account": "Create Account", "join_community": "Join our community of successful entrepreneurs", "email": "Email", "enter_email": "Enter your email", "email_required": "Email is required", "email_invalid": "Please enter a valid email", "username": "Username", "enter_username": "Enter username", "username_required": "Username is required", "password": "Password", "enter_password": "Enter password", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters", "confirm_password": "Confirm Password", "enter_confirm_password": "Confirm your password", "confirm_password_required": "Please confirm your password", "passwords_not_match": "Passwords do not match", "terms_agree": "I agree to the Terms of Service and Privacy Policy", "terms_required": "You must agree to the terms and conditions", "register": "Create Account", "registering": "Creating account...", "already_have_account": "Already have an account?", "login": "Sign in", "password_no_space": "Password must not contain spaces", "password_invalid_characters": "Password must contain only letters, numbers, and basic special characters"}, "landing": {"header": {"services": "Services", "features": "Features", "pricing": "Pricing", "login": "<PERSON><PERSON>", "signup": "Sign Up"}, "hero": {"title": "The most comfortable SMM-provider", "subtitle": "We will make you popular with our high-quality social media services", "signup_button": "Sign Up Now", "dashboard_button": "Dashboard"}, "stats": {"happy_customers": "Thousands of happy customers", "completed_orders": "Millions of completed orders", "quality_services": "High quality services"}, "services": {"title": "Huge service assortment", "subtitle": "In all of popular social networks", "view_all": "View All Services", "followers": "Followers", "likes": "<PERSON>s", "views": "Views"}, "features": {"title": "We have everything you need", "subtitle": "High quality services, convenient panel, great support and our API", "stats": "Real-time statistics", "stats_desc": "Monitor your orders and account activity with detailed analytics and reports.", "payments": "Secure payments", "payments_desc": "Multiple payment methods with secure processing and transaction history.", "api": "API integration", "api_desc": "Easily integrate our services with your applications using our robust API.", "support": "24/7 Support", "support_desc": "Our dedicated support team is always ready to assist you with any questions."}, "quality": {"title": "Amazing quality and affordable prices", "amazing_quality": "Amazing quality", "amazing_quality_desc": "We monitor our services all the time. If quality does not match expectations, you will get full refund.", "affordable_prices": "Affordable prices", "affordable_prices_desc": "Our panel works with direct suppliers only, you get cheapest prices from 400+ items.", "get_started": "Get Started", "price_prefix": "$"}, "footer": {"slogan": "The most comfortable SMM-provider", "copyright": "© 2023 NEWPANEL. All rights reserved.", "terms": "Terms of Service", "privacy": "Privacy Policy", "contact": "Contact"}}, "affiliates": {"affiliate_program": "Affiliate Program", "affiliates": "Affiliates", "payment_history": "Payment History", "referral_link": "Referral link:", "copy_your_link": "COPY YOUR LINK", "copied": "COPIED!", "commission_rate": "Commission Rate", "affiliate_system": "Affiliate System", "loyalty_program": "Loyalty program and percentage", "enable_on_panel": "Enable on panel", "set_percentage": "Set percentage of referral income", "save": "Save", "total_referrals": "Total Referrals", "commission_earnings": "Commission Earnings", "successful_referrals": "Successful Referrals", "no_referrals": "No referrals found", "share_link": "Share your referral link to start earning commissions", "payment_history_empty": "Your affiliate payment history will appear here", "program_description": "Earn money by referring new users to our platform. You'll receive a commission for every purchase they make.", "share_link_info": "Share this link with your friends, on social media, or on your website to earn commissions.", "per_referral": "per referral", "active_users": "active users", "total_earned": "total earned", "current_balance": "current balance", "commission_info": "Commissions are calculated based on your referrals' purchases", "invite_friends": "Invite Friends", "payment_history_description": "Track all your affiliate earnings and payment transactions in one place.", "no_transactions": "No Transactions Yet", "request_payment": "Request Payment", "enable_affiliate": "Enable Affiliate System", "percentage_affiliate": "Affiliate Percentage", "default_rate": "Default Rate", "custom_rate": "Custom Rate", "pending_commissions": "Pending Commissions", "paid_commissions": "Paid Commissions", "commission_status": "Commission Status", "pending": "Pending", "paid": "Paid", "referral_code": "Referral Code", "referred_user": "Referred User", "transaction_note": "Transaction Note", "commission_amount": "Commission Amount", "user_referrals": "User Referrals", "referral_stats": "Referral Statistics", "my_referrals": "My Referrals", "my_commissions": "My Commissions", "affiliate_link": "Affiliate Link", "effective_rate": "Effective Rate", "using_custom_rate": "Using custom rate", "using_default_rate": "Using tenant default rate"}, "general": {"back": "Back"}, "currency_settings": {"page_description": "Manage currency settings, exchange rates, and synchronization options", "please_enter_valid_rate": "Please enter a valid exchange rate", "rate_already_updated": "Exchange rate is already up to date", "cannot_set_custom_rate_sync_enabled": "Cannot set custom rate when sync is enabled. Please disable sync first."}, "chat": {"live_chat": "Live Chat", "support_chat": "Support Chat", "new_chat": "New Chat", "start_new_chat": "Start New Chat", "conversations": "Conversations", "no_conversations": "No conversations yet", "no_messages": "No messages yet", "start_conversation": "Start a conversation with our support team", "start_chat_hint": "Start a new chat by clicking the + button", "type_message": "Type a message...", "type_support_message": "Type your message to support...", "direct_chat": "Direct Chat", "group_chat": "Group Chat", "online": "Online", "offline": "Offline", "loading_more": "Loading more messages...", "no_more_messages": "No more messages", "loading_messages": "Loading messages...", "attach_file": "Attach file", "file_selected": "File selected", "remove_file": "Remove file", "file_too_large": "File size must be less than 10MB", "file_type_not_supported": "File type not supported. Please select an image, PDF, or document file.", "only_images_allowed": "Only image files are allowed", "download_file": "Download file", "new_message": "New message", "new_messages": "new messages"}, "payment_methods": {"edit_description": "Edit payment system description", "payment_limit": "Payment limit", "set_bonus": "Set bonus", "description": "Description", "current_limits": "Current limits", "current_bonus": "Current bonus", "min": "Min", "max": "Max", "min_amount": "Minimum amount", "max_amount": "Maximum amount", "bank": "Bank", "name": "Name", "security_key": "Security Key", "leave_empty_to_keep_current": "Leave empty to keep current", "leave_empty_security_key": "Leave empty to keep current security key", "rate": "Rate", "icon": "Icon", "creating": "Creating...", "updating": "Updating...", "cancel": "Cancel", "display": "Display", "bonus_amount": "Bonus amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "bonus_conditions": "Bonus conditions", "add_bonus": "Add bonus", "bonus_tier": "Bonus tier", "from": "From", "bonus_percentage": "Bonus percentage", "exchange_rate": "Exchange rate"}, "Left": "Left", "Right": "Right", "go_to_panel": "Go to Panel", "go_to_admin": "Go to Admin", "showing": "Showing", "to": "to", "of": "of", "results": "results", "previous": "Previous", "next": "Next", "reset_filters": "Reset Filters", "dashboard": {"title": "Dashboard V2", "subtitle": "Comprehensive analytics and insights", "kpi_section": "Key Performance Indicators", "today_section": "Today's Statistics", "historical_section": "Historical Statistics", "statistics_title": "Statistics", "historical_statistics": "Historical Statistics", "commissions": "Commissions", "bank_logs": "Bank Logs", "custom_date_range": "Custom", "service_analysis": "Service Analysis", "total_deposits": "Total Deposits", "total_spending": "Total Spending", "total_users": "Total Users", "total_orders": "Total Orders", "total_user_balance": "Total User Balance", "pending_support": "Pending Support", "today_deposits": "Today's Deposits", "today_spending": "Today's Spending", "today_new_users": "Today's New Users", "today_orders": "Today's Orders", "today_pending_support": "Today's Pending Support", "total_bonus": "Total Bonus", "today_bonus": "Today's Bonus", "deposits_chart": "Deposits Chart", "orders_chart": "Orders Chart", "tickets_chart": "Tickets Chart", "services_chart": "Services Chart", "deposits_chart_title": "Deposit Statistics", "orders_chart_title": "Order Statistics", "tickets_chart_title": "Support Ticket Statistics", "services_chart_title": "Top 10 Services", "top_services": "Top Services", "top_10_services": "Top 10 Services", "service_data_table": "Service Data Table", "service_name": "Service Name", "total_orders_count": "Total Orders", "total_revenue": "Total Revenue", "total_quantity": "Total Quantity", "category": "Category", "platform": "Platform", "select_service": "Select Service", "search_services": "Search services...", "please_search_select_service": "Please search and select a service to view detailed analysis", "service_details": "Service Details", "total_refunded": "Total Refunded", "refunded_amount": "Refunded Amount", "order_list": "Order List", "order_id": "Order ID", "username": "Username", "order_date": "Order Date", "link": "Link", "quantity": "Quantity", "price": "Price", "status": "Status", "previous": "Previous", "next": "Next", "page": "Page", "of": "of", "loading": "Loading...", "no_data": "No data available", "error_loading": "Error loading data", "commission_transaction_id": "Transaction ID", "commission_referrer": "<PERSON><PERSON><PERSON>", "commission_referred_user": "Referred User", "commission_amount": "Commission Amount", "commission_status": "Status", "commission_created_date": "Date Created", "no_commission_data": "No commission data available", "no_commission_data_note": "No commissions found in the selected time period", "showing": "Showing", "to": "to", "of_total": "of total", "results": "results"}, "revenue_charts": {"title": "Revenue Statistics", "subtitle": "Track daily revenue visually", "current_period": "Month {{month}} Year {{year}}", "view_chart": "Chart", "view_table": "Table", "loading_data": "Loading data...", "filter_by_service": "Filter by Service", "all_services": "All Services", "total_revenue": "Total Revenue", "average_daily": "Average/Day", "highest_day": "Highest", "daily_revenue": "Daily Revenue", "chart_title": "Revenue Statistics by Day of Month", "day_in_month": "Day in Month", "revenue_vnd": "Revenue (VND)", "month": "Month", "total_year": "Total Year {{year}}", "annual_revenue": "Annual Revenue", "has_data": "(has data)", "no_data": "No data", "month_names": {"1": "January", "2": "February", "3": "March", "4": "April", "5": "May", "6": "June", "7": "July", "8": "August", "9": "September", "10": "October", "11": "November", "12": "December"}}, "position_selection": {"title": "Select Position", "select_position": "Choose where to display", "home_page": "Home Page", "home_description": "Display on the main dashboard page", "add_fund_page": "Add Fund Page", "add_fund_description": "Display on the add funds page"}, "discount_system": {"title": "Discount System ( Rank - Position )", "description": "Discounts for the most active customers on your panel ( Rank - Position )", "manage_statuses": "Manage Discount Statuses", "manage_description": "Create and manage discount statuses based on customer spending", "enable_on_panel": "Enable on panel", "add_status": "Add status", "create_status": "Create status", "edit_status": "Edit status", "status_name": "Status name", "status_name_placeholder": "Enter status name", "status_icon": "Status icon", "recommended_size": "Recommended size 36×36px", "upload_own": "Upload own", "choose_icon": "<PERSON><PERSON>", "upload_icon": "Upload Icon", "expense_from": "From", "expense_to": "To", "expense_from_note": "Expense ranges must be continuous starting from 0", "expense_to_note": "Leave empty for no upper limit (∞)", "status_discount": "Status discount", "no_statuses": "No discount statuses", "no_statuses_description": "Create your first discount status to reward your most active customers"}, "payment_method_statistics": {"title": "Payment Method Statistics", "subtitle": "Track payment method activities and transactions", "provider_statistics": "Statistics by Provider", "transaction_statistics": "Transaction Statistics by Payment Method", "history_title": "Payment Method Change History", "no_transaction_data": "No transaction data available", "transaction_data_note": "Data will be displayed when there are deposit transactions via payment methods", "no_history_data": "No history data available", "no_history_note": "No changes in the selected time period", "transaction_list_title": "Transaction List", "search_by_username": "Search by username", "reset": "Reset", "transaction_id": "ID", "username": "Username", "transaction_type": "Type", "amount_received": "Amount Received", "deposit_amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "payment_method": "Payment Method", "bank_transaction_code": "Bank Transaction Code", "transaction_time": "Time", "no_transaction_list_data": "No transaction data", "try_change_filters": "Try changing filters or time range", "previous_page": "Previous", "next_page": "Next", "top_10_payment_methods": "Top 10 Payment Methods by <PERSON><PERSON><PERSON><PERSON> Amount", "no_change_details": "No change details available", "provider": "Provider", "bank": "Bank", "total_amount": "Total Amount", "transaction_count": "Transaction Count", "average_amount": "Average", "last_transaction": "Last Transaction", "status": "Status", "active": "Active", "inactive": "Inactive", "action": "Action", "changes": "Changes", "changed_fields": "Changed Fields", "old_value": "Old", "new_value": "New", "action_types": {"all": "All Actions", "CREATE": "Create", "UPDATE": "Update", "DELETE": "Delete", "ACTIVATE": "Activate", "DEACTIVATE": "Deactivate"}, "chart_labels": {"total_amount_vnd": "Total Amount (VND)", "total_amount": "Total Amount", "transaction_count": "Transaction Count", "average": "Average"}, "loading_statistics": "Loading statistics...", "loading_history": "Loading history...", "error_loading_statistics": "Error loading statistics", "error_loading_history": "Error loading history", "no_matching_transactions": "No matching transactions found", "try_change_search": "Try changing your search criteria", "try_different_filters": "Try different filters", "searching_transactions": "Searching transactions..."}, "dashboard_v2": {"tabs": {"statistics": "Statistics", "commissions": "Commissions", "bank_logs": "Bank Logs"}, "preset_labels": {"today": "Today", "yesterday": "Yesterday", "7days": "Last 7 days", "30days": "Last 30 days", "thisMonth": "This Month", "lastMonth": "Last Month"}, "commission_status": {"PAID": "Paid", "PENDING": "Pending"}}, "admin_commissions": {"title": "Commission Management", "subtitle": "Track and manage all commissions from affiliate program", "status_options": {"all": "All Status", "PENDING": "Pending", "PAID": "Paid"}, "status_text": {"PAID": "Paid", "PENDING": "Pending"}, "table_headers": {"transaction_id": "Transaction ID", "referrer": "<PERSON><PERSON><PERSON>", "referred_user": "Referred User", "commission_amount": "Commission Amount", "status": "Status", "date": "Date Created"}, "no_data": "No commission data available", "loading": "Loading data...", "error_loading": "Error loading commission data"}, "otp": {"code": "OTP Code", "placeholder": "Enter 6-digit OTP", "required": "OTP is required", "pattern": "OTP must be 6 digits", "submit": "Submit", "submitting": "Submitting...", "cancel": "Cancel"}, "autovnfb": {"add_title": "Add Payment Method", "edit_title": "Edit Payment Method", "name": "Name", "name_placeholder": "Payment method name", "bank": "Bank", "select_bank": "Select bank", "username": "Username", "vcb_username": "VCB Account", "username_placeholder": "Enter your bank username", "password": "Password", "vcb_password": "VCB Password", "password_placeholder": "Enter your bank password", "account_number": "Account Number", "vcb_account_number": "VCB Account Number", "account_number_placeholder": "Account number", "account_name": "Account Name", "account_name_placeholder": "Account holder name", "exchange_rate": "Exchange Rate", "exchange_rate_placeholder": "1.0", "rate": "Rate", "icon": "Icon", "use_bank_icon": "Use bank icon", "icon_placeholder": "Icon URL or upload", "upload_from_computer": "Upload from computer", "choose_from_library": "Choose from library", "webhook_url": "Webhook URL", "webhook_placeholder": "https://vietnamfb.com/api/payments/webhook", "cancel": "Cancel", "submit": "Submit", "update": "Update", "get_otp": "Get OTP", "getting_otp": "Getting OTP...", "creating": "Creating...", "updating": "Updating...", "vcb_otp_title": "VCB OTP Verification", "vcb_otp_message": "Please enter the OTP code sent to your registered phone number", "name_required": "Name is required", "name_too_short": "Name is too short", "name_too_long": "Name is too long", "bank_code_required": "Bank code is required", "username_required": "Username is required", "password_required": "Password is required", "account_number_required": "Account number is required", "account_number_too_short": "Account number is too short", "account_number_too_long": "Account number is too long", "account_name_required": "Account name is required", "exchange_rate_required": "Exchange rate is required", "exchange_rate_too_small": "Exchange rate is too small", "exchange_rate_too_large": "Exchange rate is too large"}, "add_fund": {"payment_note": "Note: Payment may take 5-15 minutes to process. Contact support via Telegram example", "member_rank": "Member Rank", "total_deposit": "Total Deposit", "total_bonus": "Total Bonus", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "payment_method": "Choose your preferred payment method", "select_payment_method": "Select payment method", "search_payment_methods": "Search payment methods...", "no_payment_methods_found": "No payment methods found", "scan_qr": "Scan QR code to pay", "transfer_to": "Transfer to", "to": "to", "bank": "Bank", "account": "Account Number", "name": "Name", "content": "Content", "transfer_content": "Transfer Content", "crypto": "Crypto", "register_marketplace": "Register to connect with marketplace website", "amount": "Amount", "custom_amount": "Custom Amount", "amount_limits": "Amount limits", "pay": "Pay", "transaction_history": "Transaction History", "transaction_id": "Transaction ID", "date": "Date", "type": "Type", "method": "Method", "transaction_amount": "Amount", "bonus_conditions": "Bonus Conditions", "bonus": "Bonus", "no_transactions": "No transactions found", "date_range": "Date Range", "dripfeed": {"title": "Dripfeed Management", "user": "User", "service": "Service", "link": "Link", "quantity": "Quantity", "schedule": "Schedule", "loop_settings": "Loop Settings", "status": "Status", "orders": "Orders", "actions": "Actions", "no_dripfeeds": "No dripfeeds found", "no_dripfeeds_description": "No users have created any scheduled orders yet.", "search_placeholder": "Search by link, ID, or user..."}}, "PAYMENT_HISTORY": {"TITLE": "Payment History", "TRANSACTION_DATE": "Transaction Date", "REFERENCE": "Reference", "TYPE": "Type", "AMOUNT": "Amount", "DESCRIPTION": "Description", "NO_TRANSACTIONS": "No transactions", "NO_TRANSACTIONS_DESC": "No transactions have been made for this payment method yet."}, "balance_alert": {"title": "Provider balance notifications", "label": "Set balance limit", "placeholder": "Enter balance limit", "info_text": "You will receive notifications once in 24 hours", "change_button": "Change", "no_provider_selected": "No provider selected", "save_error": "Failed to change balance alert. Please try again."}}