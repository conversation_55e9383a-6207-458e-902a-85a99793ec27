import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { ChildPanelTenant, TenantService } from '../../../core/services/tenant.service';
import { ToastService } from '../../../core/services/toast.service';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from '../../../core/services/config.service';
import { Subscription } from 'rxjs';
// import { AdminMenuComponent, AdminMenuItem } from '../../common/admin-menu/admin-menu.component';
import { TimezonePipe } from '../../../shared/pipes/timezone.pipe';



@Component({
  selector: 'app-child-panels-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TimezonePipe,
    TranslateModule
  ],
  templateUrl: './child-panels-management.component.html',
  styleUrls: ['./child-panels-management.component.css']
})
export class ChildPanelsManagementComponent implements OnInit, OnDestroy {
  // Make Math available in template
  Math = Math;

  // Child panels data
  childPanels: ChildPanelTenant[] = [];
  searchTerm: string = '';
  
  // Loading state
  isLoading = false;

  // Current tenant
  currentTenantId: string | null = null;

  // Menu actions (simplified without AdminMenuComponent)
  // menuActions: AdminMenuItem[] = [
  //   { id: 'promote', label: 'admin.child_panels.promote_to_parent', icon: 'arrow-up', iconColor: 'text-blue-500' },
  //   { id: 'move', label: 'admin.child_panels.move_to_parent', icon: 'exchange-alt', iconColor: 'text-orange-500' },
  //   { id: 'divider', label: '', divider: true },
  //   { id: 'delete', label: 'admin.child_panels.delete_child_panel', icon: 'trash', iconColor: 'text-red-500' }
  // ];

  // Selected tenant for actions
  selectedTenant: ChildPanelTenant | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    private tenantService: TenantService,
    private toastService: ToastService,

  ) {}

  ngOnInit() {
    // Load child panels on init
    this.loadChildPanels();

    // Subscribe to current tenant changes to reload data
    this.subscriptions.push(
      this.tenantService.currentTenant$.subscribe(tenant => {
        if (tenant) {
          this.currentTenantId = typeof tenant === 'string' ? tenant : tenant.id;
          this.loadChildPanels();
        }
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Load child panels for current tenant
  loadChildPanels() {
    this.isLoading = true;

    // Use current tenant context from backend, no need to pass tenant ID in URL
    this.tenantService.loadChildPanels().subscribe({
      next: (childPanels) => {
        this.childPanels = this.filterChildPanels(childPanels);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading child panels:', error);
        this.toastService.showError('Failed to load child panels');
        this.isLoading = false;
      }
    });
  }

  // Filter child panels based on search term
  filterChildPanels(panels: ChildPanelTenant[]): ChildPanelTenant[] {
    if (!this.searchTerm.trim()) {
      return panels;
    }

    const searchLower = this.searchTerm.toLowerCase().trim();
    return panels.filter(panel => 
      panel.domain.toLowerCase().includes(searchLower) ||
      panel.contact_email?.toLowerCase().includes(searchLower) ||
      panel.status.toLowerCase().includes(searchLower)
    );
  }

  search() {
    this.loadChildPanels();
  }

  resetSearch() {
    this.searchTerm = '';
    this.loadChildPanels();
  }



  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'waiting':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Menu action handlers (simplified)
  // handleMenuAction(action: string, tenant: ChildPanelTenant) {
  //   this.selectedTenant = tenant;

  //   switch (action) {
  //     case 'promote':
  //       this.promoteToParent(tenant);
  //       break;
  //     case 'move':
  //       this.moveToParent(tenant);
  //       break;
  //     case 'delete':
  //       this.deleteChildPanel(tenant);
  //       break;
  //   }
  // }



  deleteChildPanel(tenant: ChildPanelTenant) {
    if (confirm(`Are you sure you want to permanently delete child panel "${tenant.domain}"? This action cannot be undone.`)) {
      // Note: You might need to implement a delete endpoint in the backend
      this.toastService.showWarning('Delete functionality not implemented yet');
    }
  }

  // Get admin credentials from cache
  getAdminCredentials(domain: string): any {
    const credentialsKey = `child_panel_credentials_${domain}`;
    const stored = localStorage.getItem(credentialsKey);
    
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.error('Error parsing stored credentials:', error);
      }
    }
    
    return null;
  }

  // Show admin credentials
  showCredentials(tenant: ChildPanelTenant) {
    const credentials = this.getAdminCredentials(tenant.domain);
    
    if (credentials) {
      alert(`Admin Credentials for ${tenant.domain}:\nUsername: ${credentials.adminName}\nPassword: ${credentials.adminPassword}`);
    } else {
      this.toastService.showWarning('No cached credentials found for this child panel');
    }
  }
}
