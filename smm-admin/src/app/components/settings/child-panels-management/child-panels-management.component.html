<!-- Header -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">{{ 'admin.child_panels.title' | translate }}</h1>
      <p class="mt-1 text-sm text-gray-600">{{ 'admin.child_panels.description' | translate }}</p>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="mb-4 flex flex-col sm:flex-row gap-4">
    <div class="flex-1">
      <div class="relative">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (keyup.enter)="search()"
          placeholder="{{ 'admin.child_panels.search_placeholder' | translate }}"
          class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        <fa-icon [icon]="['fas', 'search']" class="absolute left-3 top-3 text-gray-400"></fa-icon>
      </div>
    </div>
    <div class="flex gap-2">
      <button
        (click)="search()"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        <fa-icon [icon]="['fas', 'search']" class="mr-2"></fa-icon>
        {{ 'search' | translate }}
      </button>
      <button
        (click)="resetSearch()"
        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
        <fa-icon [icon]="['fas', 'times']" class="mr-2"></fa-icon>
        {{ 'admin.reset' | translate }}
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-12">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span class="ml-3 text-gray-600">{{ 'admin.child_panels.loading' | translate }}</span>
  </div>

  <!-- Child Panels Table - Desktop View -->
  <div *ngIf="!isLoading" class="hidden lg:block bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.child_panels.domain' | translate }}</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.child_panels.subscription_end_date' | translate }}</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.child_panels.days_until_expiration' | translate }}</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.child_panels.created_at' | translate }}</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.child_panels.status' | translate }}</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.child_panels.actions' | translate }}</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let panel of childPanels" class="hover:bg-gray-50 cursor-pointer">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium">
                {{ panel.domain }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div *ngIf="panel.subscription_end_date">
                {{ panel.subscription_end_date | date:'short' }}
              </div>
              <div *ngIf="!panel.subscription_end_date" class="text-gray-500">-</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <div *ngIf="panel.days_until_expiration !== undefined" [ngClass]="{
                  'text-red-600 font-medium': panel.days_until_expiration <= 7,
                  'text-yellow-600 font-medium': panel.days_until_expiration > 7 && panel.days_until_expiration <= 30,
                  'text-green-600': panel.days_until_expiration > 30
                }">
                {{ panel.days_until_expiration }} {{ 'admin.days_remaining' | translate }}
              </div>
              <div *ngIf="panel.days_until_expiration === undefined" class="text-gray-500">-</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ panel.created_at | timezone:'short' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [class]="getStatusClass(panel.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ panel.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" (click)="$event.stopPropagation()">
              <button
                (click)="deleteChildPanel(panel)"
                class="text-red-600 hover:text-red-900">
                <fa-icon [icon]="['fas', 'trash']"></fa-icon>
              </button>
            </td>
          </tr>
          <tr *ngIf="childPanels.length === 0">
            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
              {{ 'admin.no_panels_found' | translate }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Child Panels Cards - Mobile/Tablet View -->
  <div *ngIf="!isLoading" class="lg:hidden space-y-4">
    <!-- Empty State for Mobile -->
    <div *ngIf="childPanels.length === 0" class="bg-white rounded-lg shadow p-6 text-center">
      <fa-icon [icon]="['fas', 'inbox']" class="mx-auto h-12 w-12 text-gray-400 mb-4"></fa-icon>
      <h3 class="text-sm font-medium text-gray-900 mb-1">{{ 'admin.no_panels_found' | translate }}</h3>
      <p class="text-sm text-gray-500">{{ 'admin.child_panels.no_data_description' | translate }}</p>
    </div>

    <!-- Panel Cards -->
    <div *ngFor="let panel of childPanels" class="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
      <div class="p-4">
        <!-- Header with Domain and Status -->
        <div class="flex items-center justify-between mb-3">
          <div class="flex-1 min-w-0">
            <h3 class="text-sm font-medium text-gray-900 truncate">{{ panel.domain }}</h3>
          </div>
          <div class="ml-2 flex-shrink-0">
            <span [class]="getStatusClass(panel.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ panel.status }}
            </span>
          </div>
        </div>

        <!-- Panel Details -->
        <div class="space-y-3">
          <!-- Subscription End Date -->
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">{{ 'admin.child_panels.subscription_end_date' | translate }}:</span>
            <span class="text-gray-900 font-medium">
              <span *ngIf="panel.subscription_end_date">{{ panel.subscription_end_date | date:'short' }}</span>
              <span *ngIf="!panel.subscription_end_date" class="text-gray-500">-</span>
            </span>
          </div>

          <!-- Days Until Expiration -->
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">{{ 'admin.child_panels.days_until_expiration' | translate }}:</span>
            <span [ngClass]="{
                'text-red-600 font-medium': panel.days_until_expiration <= 7,
                'text-yellow-600 font-medium': panel.days_until_expiration > 7 && panel.days_until_expiration <= 30,
                'text-green-600 font-medium': panel.days_until_expiration > 30,
                'text-gray-500': panel.days_until_expiration === undefined
              }">
              <span *ngIf="panel.days_until_expiration !== undefined">
                {{ panel.days_until_expiration }} {{ 'admin.days_remaining' | translate }}
              </span>
              <span *ngIf="panel.days_until_expiration === undefined">-</span>
            </span>
          </div>

          <!-- Created At -->
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">{{ 'admin.child_panels.created_at' | translate }}:</span>
            <span class="text-gray-900">{{ panel.created_at | timezone:'short' }}</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-4 pt-3 border-t border-gray-200 flex justify-end">
          <button
            (click)="deleteChildPanel(panel)"
            class="inline-flex items-center px-3 py-1.5 border border-red-300 rounded-md text-xs font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors">
            <fa-icon [icon]="['fas', 'trash']" class="mr-1.5"></fa-icon>
            {{ 'admin.child_panels.delete' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>

