import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdminPanelService } from '../../../core/services/admin-panel.service';
import { AdminPanelTenant } from '../../../model/response/admin-panel-tenant.model';
import { ToastService } from '../../../core/services/toast.service';
import { Subscription } from 'rxjs';
import { AdminMenuComponent, AdminMenuItem } from '../../common/admin-menu/admin-menu.component';
import { ExtendSubscriptionPopupComponent } from '../extend-subscription-popup/extend-subscription-popup.component';
import { TimezonePipe } from '../../../shared/pipes/timezone.pipe';

@Component({
  selector: 'app-panels-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    AdminMenuComponent,
    ExtendSubscriptionPopupComponent,
    TimezonePipe
  ],
  templateUrl: './panels-management.component.html',
  styleUrls: ['./panels-management.component.css']
})
export class PanelsManagementComponent implements OnInit, OnDestroy {
  // Make Math available in template
  Math = Math;

  @Output() openDomainInfo = new EventEmitter<{tenantId: string, domain: string}>();

  // Panels data
  panels: AdminPanelTenant[] = [];
  searchTerm: string = '';
  pagination = {
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  };

  // Loading state
  isLoading = false;

  // Menu actions
  menuActions: AdminMenuItem[] = [
    { id: 'extend', label: 'admin.admin_panel.extend_subscription', icon: 'calendar-plus', iconColor: 'text-blue-500' },
    { id: 'disable', label: 'admin.admin_panel.disable_tenant', icon: 'ban', iconColor: 'text-orange-500' },
    { id: 'enable', label: 'admin.admin_panel.enable_tenant', icon: 'check-circle', iconColor: 'text-green-500' },
    { id: 'divider', label: '', divider: true },
    { id: 'delete', label: 'admin.admin_panel.delete_tenant', icon: 'trash', iconColor: 'text-red-500' }
  ];

  // Popup states
  showExtendPopup = false;
  selectedTenant: AdminPanelTenant | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    private adminPanelService: AdminPanelService,
    private toastService: ToastService
  ) {}

  ngOnInit() {
    // Subscribe to panels data
    this.subscriptions.push(
      this.adminPanelService.panels$.subscribe(panels => {
        this.panels = panels;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.adminPanelService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );

    // Subscribe to panels pagination
    this.subscriptions.push(
      this.adminPanelService.panelsPagination$.subscribe(pagination => {
        this.pagination = pagination;
      })
    );

    // Load initial data
    this.loadPanels();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Panels methods
  loadPanels(page: number = 0) {
    this.adminPanelService.getAllPanels(page, this.pagination.page_size, this.searchTerm)
      .subscribe({
        error: (error) => {
          console.error('Error loading panels:', error);
          this.toastService.showError('Failed to load panels');
        }
      });
  }

  search() {
    this.loadPanels(0);
  }

  resetSearch() {
    this.searchTerm = '';
    this.loadPanels(0);
  }

  // Pagination methods
  loadPage(page: number) {
    this.loadPanels(page);
  }

  // Domain info modal
  onOpenDomainInfo(tenantId: string, domain: string) {
    this.openDomainInfo.emit({ tenantId, domain });
  }

  // Utility methods
  formatDate(dateString: string): string {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }

  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'activated':
        return 'bg-green-100 text-green-800';
      case 'suspended':
      case 'deactivated':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Menu action handlers
  handleMenuAction(action: string, tenant: AdminPanelTenant) {
    this.selectedTenant = tenant;

    switch (action) {
      case 'extend':
        this.showExtendPopup = true;
        break;
      case 'disable':
        this.disableTenant(tenant);
        break;
      case 'enable':
        this.enableTenant(tenant);
        break;
      case 'delete':
        this.deleteTenant(tenant);
        break;
    }
  }

  // Tenant management methods
  disableTenant(tenant: AdminPanelTenant) {
    if (confirm(`Are you sure you want to disable tenant "${tenant.domain}"?`)) {
      this.adminPanelService.disableTenant(tenant.id)
        .subscribe({
          next: () => {
            this.toastService.showSuccess(`Tenant "${tenant.domain}" has been disabled`);
            this.loadPanels();
          },
          error: (error) => {
            console.error('Error disabling tenant:', error);
            this.toastService.showError('Failed to disable tenant');
          }
        });
    }
  }

  enableTenant(tenant: AdminPanelTenant) {
    this.adminPanelService.enableTenant(tenant.id)
      .subscribe({
        next: () => {
          this.toastService.showSuccess(`Tenant "${tenant.domain}" has been enabled`);
          this.loadPanels();
        },
        error: (error) => {
          console.error('Error enabling tenant:', error);
          this.toastService.showError('Failed to enable tenant');
        }
      });
  }

  deleteTenant(tenant: AdminPanelTenant) {
    if (confirm(`Are you sure you want to permanently delete tenant "${tenant.domain}"? This action cannot be undone.`)) {
      this.adminPanelService.deleteTenant(tenant.id)
        .subscribe({
          next: () => {
            this.toastService.showSuccess(`Tenant "${tenant.domain}" has been deleted`);
            this.loadPanels();
          },
          error: (error) => {
            console.error('Error deleting tenant:', error);
            this.toastService.showError('Failed to delete tenant');
          }
        });
    }
  }

  // Popup handlers
  onExtendPopupClose() {
    this.showExtendPopup = false;
    this.selectedTenant = null;
  }

  onExtensionComplete() {
    this.loadPanels();
  }

  // Get filtered menu actions based on tenant status
  getMenuActionsForTenant(tenant: AdminPanelTenant): AdminMenuItem[] {
    const actions = [...this.menuActions];

    // Hide enable/disable based on current status
    if (tenant.status?.toLowerCase() === 'suspended') {
      // Hide disable action for suspended tenants
      return actions.filter(action => action.id !== 'disable');
    } else {
      // Hide enable action for active tenants
      return actions.filter(action => action.id !== 'enable');
    }
  }
}
