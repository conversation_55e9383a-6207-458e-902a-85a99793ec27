<!-- Search and Filters -->
<div class="mb-4 flex flex-col sm:flex-row gap-4">
  <div class="flex-1">
    <div class="relative">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        (keyup.enter)="search()"
        placeholder="{{ 'admin.search_by_domain' | translate }}"
        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
      <fa-icon [icon]="['fas', 'search']" class="absolute left-3 top-3 text-gray-400"></fa-icon>
    </div>
  </div>
  <div class="flex gap-2">
    <button
      (click)="search()"
      class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
      <fa-icon [icon]="['fas', 'search']" class="mr-2"></fa-icon>
      {{ 'search' | translate }}
    </button>
    <button
      (click)="resetSearch()"
      class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
      <fa-icon [icon]="['fas', 'times']" class="mr-2"></fa-icon>
      {{ 'admin.reset' | translate }}
    </button>
  </div>
</div>

<!-- Panels Table -->
<div class="bg-white rounded-lg shadow overflow-hidden">
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.domain' | translate }}</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.owner' | translate }}</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.status' | translate }}</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.subscription' | translate }}</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.auto_renewal' | translate }}</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.created' | translate }}</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ 'admin.actions' | translate }}</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let panel of panels" class="hover:bg-gray-50 cursor-pointer" (click)="onOpenDomainInfo(panel.id, panel.domain)">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-blue-500">
              {{ panel.domain }}
            </div>
            <div class="text-sm text-gray-500">{{ panel.id }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <div *ngIf="panel.owner_user_name" class="text-sm font-medium text-gray-900">
              {{ panel.owner_user_name }}
            </div>
            <div *ngIf="panel.owner_email" class="text-sm text-gray-500">
              {{ panel.owner_email }}
            </div>
            <div *ngIf="!panel.owner_user_name" class="text-gray-500">-</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span [class]="getStatusClass(panel.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ panel.status }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <div *ngIf="panel.subscription_end_date">
              {{ 'admin.expires' | translate }}: {{ formatDate(panel.subscription_end_date) }}
            </div>
            <div *ngIf="!panel.subscription_end_date" class="text-gray-500">-</div>
            <div *ngIf="panel.days_remaining !== undefined" [ngClass]="{
                'text-red-600 font-medium': panel.days_remaining <= 7,
                'text-yellow-600 font-medium': panel.days_remaining > 7 && panel.days_remaining <= 30,
                'text-green-600': panel.days_remaining > 30
              }">
              {{ panel.days_remaining }} {{ 'admin.days_remaining' | translate }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <span *ngIf="panel.auto_renewal" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
              {{ 'admin.enabled' | translate }}
            </span>
            <span *ngIf="!panel.auto_renewal" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
              {{ 'admin.disabled' | translate }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ panel.created_at | timezone:'short' }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" (click)="$event.stopPropagation()">
            <app-admin-menu
              [menuItems]="getMenuActionsForTenant(panel)"
              (menuItemClicked)="handleMenuAction($event, panel)">
              <fa-icon [icon]="['fas', 'ellipsis-v']" class="text-gray-400 hover:text-gray-600"></fa-icon>
            </app-admin-menu>
          </td>
        </tr>
        <tr *ngIf="panels.length === 0 && !isLoading">
          <td colspan="7" class="px-6 py-4 text-center text-gray-500">
            {{ 'admin.no_panels_found' | translate }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <div *ngIf="pagination.total_pages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
    <div class="flex-1 flex justify-between sm:hidden">
      <button
        (click)="loadPage(pagination.page_number - 1)"
        [disabled]="pagination.page_number === 0"
        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
        {{ 'admin.previous' | translate }}
      </button>
      <button
        (click)="loadPage(pagination.page_number + 1)"
        [disabled]="pagination.page_number >= pagination.total_pages - 1"
        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
        {{ 'admin.next' | translate }}
      </button>
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-gray-700">
          {{ 'admin.showing' | translate }} {{ pagination.page_number * pagination.page_size + 1 }} {{ 'admin.to' | translate }}
          {{ Math.min((pagination.page_number + 1) * pagination.page_size, pagination.total_elements) }} {{ 'admin.of' | translate }}
          {{ pagination.total_elements }} {{ 'admin.results' | translate }}
        </p>
      </div>
      <div>
        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
          <button
            (click)="loadPage(pagination.page_number - 1)"
            [disabled]="pagination.page_number === 0"
            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
            <fa-icon [icon]="['fas', 'chevron-left']"></fa-icon>
          </button>
          <button
            (click)="loadPage(pagination.page_number + 1)"
            [disabled]="pagination.page_number >= pagination.total_pages - 1"
            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
            <fa-icon [icon]="['fas', 'chevron-right']"></fa-icon>
          </button>
        </nav>
      </div>
    </div>
  </div>
</div>

<!-- Extend Subscription Popup -->
<app-extend-subscription-popup
  *ngIf="showExtendPopup && selectedTenant"
  [tenantId]="selectedTenant.id"
  [tenantDomain]="selectedTenant.domain"
  (close)="onExtendPopupClose()"
  (extended)="onExtensionComplete()">
</app-extend-subscription-popup>

