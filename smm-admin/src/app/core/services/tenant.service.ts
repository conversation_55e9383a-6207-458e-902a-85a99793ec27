import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { ConfigService } from './config.service';
import { AuthService } from './auth.service';
import { tap, shareReplay } from 'rxjs/operators';
import { JwtHelperService } from '@auth0/angular-jwt';
import { Tokens } from '../../model/tokens.model';
import { Tenant } from '../../model/tenant.model';

export interface TenantInfo {
  id: string;
  name: string;
  domain: string;
  status: string;
  subscription_start_date: string;
  subscription_end_date: string;
  days_until_expiration: number;
  expiration_alert: string;
  auto_renewal: boolean;
  subscription_plan: string;
  contact_email: string;
  decimal_places: number;
  enable_discount_system: boolean;
}


export interface ChildPanelTenant {
  id: string;
  domain: string;
  status: string;
  api_url?: string;
  site_url?: string;
  contact_email?: string;
  created_at: string;
  parent_tenant_id: string;
  last_renewal_date: string;
  days_until_expiration: number;
  subscription_start_date: string;
  subscription_end_date: string;

}
export interface TenantSubscriptionDto {
  id: string;
  domain: string;
  status: string;
  subscription_start_date: string;
  subscription_end_date: string;
  days_until_expiration: number;
  expiration_alert: string;
  auto_renewal: boolean;
  subscription_plan: string;
  contact_email: string;
}



@Injectable({
  providedIn: 'root'
})
export class TenantService {
  private _currentTenant$ = new BehaviorSubject<TenantInfo | null>(null);
  private _accessibleTenants$ = new BehaviorSubject<TenantInfo[]>([]);

  // Cache for accessible tenants API call
  private _accessibleTenantsCache$: Observable<TenantInfo[]> | null = null;
  private _isInitialized = false;

  public currentTenant$ = this._currentTenant$.asObservable();
  public accessibleTenants$ = this._accessibleTenants$.asObservable();

  /**
   * Get current tenant value synchronously
   */
  public get currentTenantValue(): TenantInfo | null {
    return this._currentTenant$.value;
  }

  /**
   * Get accessible tenants value synchronously
   */
  public get accessibleTenantsValue(): TenantInfo[] {
    return this._accessibleTenants$.value;
  }

  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    private authService: AuthService,
    private jwtHelper: JwtHelperService
  ) {
    // Listen for auth cleared event to clear tenant cache
    if (typeof window !== 'undefined') {
      window.addEventListener('auth-cleared', () => {
        console.log('TenantService - Received auth-cleared event, clearing cache');
        this.clearCache();
      });
    }
    // Subscribe to auth changes to update tenant info
    this.authService.auth$.subscribe(user => {
      if (user && user.tokens && user.tokens.access_token) {
        // Only initialize once per auth session
        if (!this._isInitialized) {
          this._isInitialized = true;
          // Load accessible tenants first, then update current tenant info
          this.getAccessibleTenants().subscribe(() => {
            this.updateTenantInfoFromToken();
          });
        } else {
          // If already initialized, just update tenant info from token
          this.updateTenantInfoFromToken();
        }
      } else {
        this._currentTenant$.next(null);
        this._accessibleTenants$.next([]);
        this._accessibleTenantsCache$ = null;
        this._isInitialized = false;
      }
    });

    // Initialize tenant information if user is already authenticated
    const currentUser = this.authService.authValue;
    if (currentUser && currentUser.tokens && currentUser.tokens.access_token && !this._isInitialized) {
      this._isInitialized = true;
      this.getAccessibleTenants().subscribe(() => {
        this.updateTenantInfoFromToken();
      });
    }
  }

  /**
   * Extract tenant information from the JWT token
   */
  private updateTenantInfoFromToken(): void {
    const user = this.authService.authValue;
    if (user && user.tokens && user.tokens.access_token) {
      try {
        const tokenPayload = this.jwtHelper.decodeToken(user.tokens.access_token);
        console.log('TenantService - Token payload:', tokenPayload);

        // Extract current tenant ID
        const currentTenantId = tokenPayload.current_tenant_id;
        if (currentTenantId) {
          // Find the current tenant info from accessible tenants
          const accessibleTenants = this._accessibleTenants$.value;
          const currentTenantInfo = accessibleTenants.find(tenant => tenant.id === currentTenantId);

          if (currentTenantInfo) {
            this._currentTenant$.next(currentTenantInfo);
          } else {
            // If tenant info not found in accessible tenants, create a minimal TenantInfo object
            const minimalTenantInfo: TenantInfo = {
              id: currentTenantId,
              name: '',
              domain: '',
              status: '',
              subscription_start_date: '',
              subscription_end_date: '',
              days_until_expiration: 0,
              expiration_alert: '',
              auto_renewal: false,
              subscription_plan: '',
              contact_email: '',
              decimal_places: 2,
              enable_discount_system: false
            };
            this._currentTenant$.next(minimalTenantInfo);
          }

          if (typeof localStorage !== 'undefined') {
            localStorage.setItem('tenant_id', currentTenantId);
          }
        }

        // Extract accessible tenants from token
        // We'll use the API to get the full tenant info with domains
      } catch (error) {
        console.error('Error extracting tenant info from token:', error);
      }
    }
  }

  /**
   * Switch to a different tenant
   * @param tenantId The ID of the tenant to switch to
   */
  switchTenant(tenantId: string): Observable<Tokens> {
    return this.http.post<Tokens>(
      `${this.configService.apiUrl}/access/switch/${tenantId}`,
      {}
    ).pipe(
      tap(response => {

        // // Update the token in local storage
        const user = this.authService.authValue;
        if (user && user.tokens) {
          user.tokens.access_token = response.access_token;
          user.tokens.refresh_token = response.refresh_token;
          user.tokens.client_id = response.client_id;
          this.authService.updateUser(user);
        }

         if (typeof localStorage !== 'undefined') {
          // Clear design settings
          localStorage.removeItem('app_design_settings');

          // Clear tenant ID
        //  localStorage.removeItem('tenant_id');
//
          // Clear general settings
          localStorage.removeItem('general_settings');
        }

        // Clear cache and refresh accessible tenants after tenant switch
        this._accessibleTenantsCache$ = null;

        // Update tenant information
        this.updateTenantInfoFromToken();

        // Clear tenant-specific settings from localStorage

        // Refresh the list of accessible tenants with domain info
        this.refreshAccessibleTenants().subscribe();
      })
    );
  }

  /**
   * Reload necessary data after tenant switch without page refresh
   * This method should be called after switching tenants to update the UI
   */
  reloadDataAfterTenantSwitch(): void {
    // Update tenant information from token
    this.updateTenantInfoFromToken();

    // Force refresh the list of accessible tenants (clears cache)
    this.refreshAccessibleTenants().subscribe();

    // You can add more data refresh logic here as needed
    // For example, reload current page data based on the new tenant
  }

  /**
   * Get information about all accessible tenants
   * Uses caching to prevent multiple API calls during app initialization
   */
  getAccessibleTenants(): Observable<TenantInfo[]> {
    // Return cached observable if it exists
    if (this._accessibleTenantsCache$) {
      return this._accessibleTenantsCache$;
    }

    // Create and cache the observable
    this._accessibleTenantsCache$ = this.http.get<TenantInfo[]>(`${this.configService.apiUrl}/access/tenants/accessible`)
      .pipe(
        tap(tenants => {
          console.log('TenantService - Accessible tenants loaded from API:', tenants.length);
          this._accessibleTenants$.next(tenants);

          // Update current tenant info if we have a current tenant ID
          const currentTenant = this._currentTenant$.value;
          if (currentTenant && currentTenant.id) {
            const updatedCurrentTenant = tenants.find(tenant => tenant.id === currentTenant.id);
            if (updatedCurrentTenant) {
              this._currentTenant$.next(updatedCurrentTenant);
            }
          }
        }),
        shareReplay(1) // Cache the result and replay to new subscribers
      );

    return this._accessibleTenantsCache$;
  }

  /**
   * Force refresh accessible tenants (clears cache and makes new API call)
   */
  refreshAccessibleTenants(): Observable<TenantInfo[]> {
    console.log('TenantService - Force refreshing accessible tenants');
    this._accessibleTenantsCache$ = null;
    return this.getAccessibleTenants();
  }

  /**
   * Clear the accessible tenants cache
   * Useful when user logs out or when we need to force a fresh API call
   */
  clearCache(): void {
    console.log('TenantService - Clearing accessible tenants cache');
    this._accessibleTenantsCache$ = null;
    this._isInitialized = false;
  }

  /**
   * Get all tenant subscriptions
   */
  getAllTenantSubscriptions(): Observable<TenantSubscriptionDto[]> {
    return this.http.get<TenantSubscriptionDto[]>(`${this.configService.apiUrl}/tenant-subscriptions`);
  }

  /**
   * Toggle auto-renewal for a tenant
   */
  toggleAutoRenewal(tenantId: string, auto_renewal: boolean): Observable<any> {
    return this.http.put(`${this.configService.apiUrl}/tenant-subscriptions/${tenantId}/auto-renewal`, {
      auto_renewal
    });
  }

  /**
   * Get current tenant details
   */
  getCurrentTenant(): Observable<Tenant> {
    return this.http.get<Tenant>(`${this.configService.apiUrl}/tenant-settings/current-tenant`);
  }

  loadChildPanels(): Observable<ChildPanelTenant[]> {
    return this.http.get<ChildPanelTenant[]>(`${this.configService.apiUrl}/sub-tenants/current/children`);
  }
}
