import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { InfoChildPanelComponent } from "./info-child-panel/info-child-panel.component";
import { FaqChildPanelComponent } from "./faq-child-panel/faq-child-panel.component";
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../icons/icons.module';
import { TenantService } from '../../core/services/tenant.service';
import { ToastService } from '../../core/services/toast.service';
import { SubTenantCreateRequest, SubTenantService, SubTenantResponse } from '../../core/services/sub-tenant.service';

@Component({
  selector: 'app-new-child-panel',
  standalone: true,
  imports: [
    InfoChildPanelComponent,
    FaqChildPanelComponent,
    TranslateModule,
    CommonModule,
    ReactiveFormsModule,
    IconsModule
  ],
  templateUrl: './new-child-panel.component.html',
  styleUrl: './new-child-panel.component.css'
})
export class NewChildPanelComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // FormGroup for reactive forms
  childPanelForm!: FormGroup;

  // Price display
  priceDisplay: string = '$7.00';

  // Password visibility
  passwordVisible: boolean = false;
  confirmPasswordVisible: boolean = false;

  // Loading state
  isSubmitting: boolean = false;

  // Current tenant
  currentTenantId: string | null = null;

  // Tab management
  activeTab: 'create' | 'list' = 'create';

  // User tenants list
  userTenants: SubTenantResponse[] = [];
  isLoadingTenants: boolean = false;

  constructor(
    private fb: FormBuilder,
    private tenantService: TenantService,
    private subTenantService: SubTenantService,
    private toastService: ToastService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.subscribeToCurrentTenant();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.childPanelForm = this.fb.group({
      domainName: ['', [Validators.required, Validators.minLength(3)]],
      adminName: ['', [Validators.required, Validators.minLength(2)]],
      adminPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  // Custom validator for password confirmation
  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('adminPassword');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    
    if (confirmPassword?.hasError('passwordMismatch')) {
      delete confirmPassword.errors?.['passwordMismatch'];
      if (Object.keys(confirmPassword.errors || {}).length === 0) {
        confirmPassword.setErrors(null);
      }
    }
    
    return null;
  }

  // Format domain method - remove protocol and www
  private formatDomain(domain: string): string {
    if (!domain) return domain;
    
    let cleanDomain = domain.trim();
    
    // Remove protocol (http:// or https://)
    cleanDomain = cleanDomain.replace(/^https?:\/\//, '');
    
    // Remove www.
    cleanDomain = cleanDomain.replace(/^www\./, '');
    
    // Remove trailing slash
    cleanDomain = cleanDomain.replace(/\/$/, '');
    
    return cleanDomain;
  }

  private subscribeToCurrentTenant(): void {
    this.tenantService.currentTenant$
      .pipe(takeUntil(this.destroy$))
      .subscribe(tenant => {
        if (tenant) {
          this.currentTenantId = this.extractTenantId(tenant);
        }
      });
  }

  private extractTenantId(tenant: any): string {
    return typeof tenant === 'string' ? tenant : (tenant?.id || tenant);
  }

  togglePasswordVisibility(field: string): void {
    if (field === 'password') {
      this.passwordVisible = !this.passwordVisible;
      this.updatePasswordFieldType('password', this.passwordVisible);
    } else if (field === 'confirm') {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
      this.updatePasswordFieldType('confirm', this.confirmPasswordVisible);
    }
  }

  private updatePasswordFieldType(fieldType: string, visible: boolean): void {
    const selector = fieldType === 'password' 
      ? 'input[formControlName="adminPassword"]'
      : 'input[formControlName="confirmPassword"]';
    
    const inputField = document.querySelector(selector) as HTMLInputElement;
    if (inputField) {
      inputField.type = visible ? 'text' : 'password';
    }
  }

  // Helper methods for template
  hasError(controlName: string, errorType: string): boolean {
    const control = this.childPanelForm.get(controlName);
    return !!(control?.hasError(errorType) && (control?.dirty || control?.touched));
  }

  getErrorMessage(controlName: string): string {
    const control = this.childPanelForm.get(controlName);
    
    if (!control?.errors || (!control.dirty && !control.touched)) {
      return '';
    }

    switch (controlName) {
      case 'domainName':
        if (control.hasError('required')) return 'Domain name is required';
        if (control.hasError('minlength')) return 'Domain name must be at least 3 characters';
        break;
      
      case 'adminName':
        if (control.hasError('required')) return 'Admin name is required';
        if (control.hasError('minlength')) return 'Admin name must be at least 2 characters';
        break;
      
      case 'adminPassword':
        if (control.hasError('required')) return 'Password is required';
        if (control.hasError('minlength')) return 'Password must be at least 6 characters';
        break;
      
      case 'confirmPassword':
        if (control.hasError('required')) return 'Please confirm your password';
        if (control.hasError('passwordMismatch')) return 'Passwords do not match';
        break;
    }
    
    return '';
  }

  submitForm(): void {
    // Mark all fields as touched to show validation errors
    this.childPanelForm.markAllAsTouched();

    if (!this.childPanelForm.valid) {
      console.log('Form validation failed', this.childPanelForm.errors);
      return;
    }

    this.isSubmitting = true;

    // Get form values
    const formValue = this.childPanelForm.value;
    
    // Format domain before sending
    const formattedDomain = this.formatDomain(formValue.domainName);
    
    // Create sub-tenant request
    const request: SubTenantCreateRequest = {
      domain: formattedDomain, // Use formatted domain
      admin_name: formValue.adminName,
      admin_password: formValue.adminPassword
    };

    // Call API to create sub-tenant
    this.subTenantService.createSubTenant(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Sub-tenant created successfully', response);
          this.toastService.showSuccess('Child panel created successfully! Status: WAITING');
          this.resetForm();
          this.isSubmitting = false; // Reset submitting state on success
        },
        error: (error) => {
          console.error('Error creating sub-tenant:', error);
          const errorMessage = error?.message || 'Unknown error occurred';
          this.toastService.showError(`${errorMessage}`);
          this.isSubmitting = false; // Reset submitting state on error
        },
        complete: () => {
          // This will run after next() or error()
          // isSubmitting is already reset above, but keeping this for safety
          this.isSubmitting = false;
        }
      });
  }

  private resetForm(): void {
    this.childPanelForm.reset();
    this.passwordVisible = false;
    this.confirmPasswordVisible = false;
  }

  loadUserTenants(): void {
    this.isLoadingTenants = true;
    this.subTenantService.getUserOwnedTenants()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tenants) => {
          this.userTenants = tenants;
          this.isLoadingTenants = false;
        },
        error: (error) => {
          console.error('Error loading user tenants:', error);
          this.toastService.showError('Failed to load your tenants');
          this.isLoadingTenants = false;
        }
      });
  }

  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'waiting':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      case 'new':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'child_panel.status_active';
      case 'waiting':
        return 'child_panel.status_waiting';
      case 'suspended':
        return 'child_panel.status_suspended';
      case 'new':
        return 'child_panel.status_new';
      default:
        return 'child_panel.status_unknown';
    }
  }

  openTenant(tenant: SubTenantResponse): void {
    if (tenant.status === 'Active') {
      // Open tenant in new tab/window
      const url = `https://${tenant.domain}`;
      window.open(url, '_blank');
    }
  }
}