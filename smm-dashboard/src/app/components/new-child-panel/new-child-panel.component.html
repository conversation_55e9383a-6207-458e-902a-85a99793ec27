<div class="flex flex-col gap-6 w-full md:flex-row">
  <div class="flex-1">
    <!-- Tab Navigation -->
    <div class="mb-6">
      <div class="flex border-b border-gray-200">
        <button
          (click)="activeTab = 'create'"
          [class]="'px-4 py-2 font-medium text-sm border-b-2 transition-colors ' +
                   (activeTab === 'create' ? 'border-[var(--primary)] text-[var(--primary)]' : 'border-transparent text-gray-500 hover:text-gray-700')">
          {{ 'child_panel.create_website' | translate }}
        </button>
        <button
          (click)="activeTab = 'list'; loadUserTenants()"
          [class]="'px-4 py-2 font-medium text-sm border-b-2 transition-colors ' +
                   (activeTab === 'list' ? 'border-[var(--primary)] text-[var(--primary)]' : 'border-transparent text-gray-500 hover:text-gray-700')">
          {{ 'child_panel.my_panels' | translate }}
        </button>
      </div>
    </div>

    <!-- Create Tab -->
    <div *ngIf="activeTab === 'create'" class="header-new-child-container shadow-sm border border-gray-100">
      <div class="header">
        <div class="icon-wrapper-sm">
          <fa-icon [icon]="['fas', 'globe']" class="text-[var(--primary)]"></fa-icon>
        </div>
        <div class="title">{{ 'child_panel.create_website' | translate }}</div>
      </div>

      <div class="form-section p-4">
        <!-- Form with FormGroup -->
        <form [formGroup]="childPanelForm" (ngSubmit)="submitForm()">
          
          <div class="form-group mt-4">
            <label class="form-label">{{ 'child_panel.domain_name' | translate }}</label>
            <div class="input-wrapper">
              <i class="fas fa-link input-icon"></i>
              <input 
                type="text" 
                formControlName="domainName" 
                placeholder="Enter domain name" 
                class="input-field"
                [class.error]="hasError('domainName', 'required') || hasError('domainName', 'minlength')">
            </div>
            <small *ngIf="getErrorMessage('domainName')" class="error-message">
              {{ getErrorMessage('domainName') }}
            </small>
          </div>

          <div class="form-group">
            <label class="form-label">{{ 'child_panel.admin_name' | translate }}</label>
            <div class="input-wrapper">
              <i class="fas fa-user input-icon"></i>
              <input 
                type="text" 
                formControlName="adminName" 
                placeholder="Enter admin username" 
                class="input-field"
                [class.error]="hasError('adminName', 'required') || hasError('adminName', 'minlength')">
            </div>
            <small *ngIf="getErrorMessage('adminName')" class="error-message">
              {{ getErrorMessage('adminName') }}
            </small>
          </div>

          <div class="form-group">
            <label class="form-label">{{ 'child_panel.admin_password' | translate }}</label>
            <div class="input-wrapper">
              <i class="fas fa-lock input-icon"></i>
              <input 
                [type]="passwordVisible ? 'text' : 'password'" 
                formControlName="adminPassword" 
                placeholder="Enter password" 
                class="input-field"
                [class.error]="hasError('adminPassword', 'required') || hasError('adminPassword', 'minlength')">
              <i 
                [class]="passwordVisible ? 'fas fa-eye' : 'fas fa-eye-slash'" 
                class="toggle-password" 
                (click)="togglePasswordVisibility('password')">
              </i>
            </div>
            <small *ngIf="getErrorMessage('adminPassword')" class="error-message">
              {{ getErrorMessage('adminPassword') }}
            </small>
          </div>

          <div class="form-group">
            <label class="form-label">{{ 'child_panel.confirm_admin_password' | translate }}</label>
            <div class="input-wrapper">
              <i class="fas fa-lock input-icon"></i>
              <input 
                [type]="confirmPasswordVisible ? 'text' : 'password'" 
                formControlName="confirmPassword" 
                placeholder="Confirm password" 
                class="input-field"
                [class.error]="hasError('confirmPassword', 'required') || hasError('confirmPassword', 'passwordMismatch')">
              <i 
                [class]="confirmPasswordVisible ? 'fas fa-eye' : 'fas fa-eye-slash'" 
                class="toggle-password" 
                (click)="togglePasswordVisibility('confirm')">
              </i>
            </div>
            <small *ngIf="getErrorMessage('confirmPassword')" class="error-message">
              {{ getErrorMessage('confirmPassword') }}
            </small>
          </div>

          <div class="form-group mt-6">
            <button
              type="submit"
              [disabled]="isSubmitting || childPanelForm.invalid"
              class="btn-submit w-full gap-3 mb-4 hover:bg-[var(--primary-hover)] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
              <span *ngIf="!isSubmitting">{{ 'child_panel.create_child_panel' | translate }}</span>
              <span *ngIf="isSubmitting">{{ 'child_panel.creating' | translate }}</span>
              <span class="price-span">{{ priceDisplay }}</span>
            </button>
          </div>

        </form>
      </div>
    </div>

    <!-- List Tab -->
    <div *ngIf="activeTab === 'list'" class="header-new-child-container shadow-sm border border-gray-100">
      <div class="header">
        <div class="icon-wrapper-sm">
          <fa-icon [icon]="['fas', 'list']" class="text-[var(--primary)]"></fa-icon>
        </div>
        <div class="title">{{ 'child_panel.my_panels' | translate }}</div>
      </div>

      <div class="form-section p-4">
        <!-- Loading State -->
        <div *ngIf="isLoadingTenants" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
        </div>

        <!-- Empty State -->
        <div *ngIf="!isLoadingTenants && userTenants.length === 0" class="text-center py-8">
          <fa-icon [icon]="['fas', 'globe']" class="text-4xl text-gray-400 mb-4"></fa-icon>
          <p class="text-gray-500">{{ 'child_panel.no_panels' | translate }}</p>
        </div>

        <!-- Tenants Table -->
        <div *ngIf="!isLoadingTenants && userTenants.length > 0" class="overflow-x-auto">
          <table class="w-full table-auto">
            <thead>
              <tr class="border-b border-gray-200">
                <th class="text-left py-3 px-4 font-medium text-gray-700">{{ 'child_panel.domain' | translate }}</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">{{ 'child_panel.status' | translate }}</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">{{ 'child_panel.created_at' | translate }}</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">{{ 'child_panel.actions' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let tenant of userTenants" class="border-b border-gray-100 hover:bg-gray-50">
                <td class="py-3 px-4">
                  <div class="font-medium text-gray-900">{{ tenant.domain }}</div>
                </td>
                <td class="py-3 px-4">
                  <span [class]="getStatusClass(tenant.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ getStatusText(tenant.status) | translate }}
                  </span>
                </td>
                <td class="py-3 px-4 text-gray-600">
                  {{ tenant.created_at | date:'short' }}
                </td>
                <td class="py-3 px-4">
                  <button
                    *ngIf="tenant.status === 'Active'"
                    (click)="openTenant(tenant)"
                    class="text-[var(--primary)] hover:text-[var(--primary-hover)] text-sm font-medium">
                    {{ 'child_panel.open' | translate }}
                  </button>
                  <span *ngIf="tenant.status !== 'Active'" class="text-gray-400 text-sm">
                    {{ 'child_panel.pending' | translate }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="flex-1 flex flex-col gap-6">
    <app-info-child-panel></app-info-child-panel>
    <app-faq-child-panel></app-faq-child-panel>
  </div>
</div>