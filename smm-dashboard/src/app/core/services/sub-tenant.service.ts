import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';


export interface SubTenantCreateRequest {
    domain: string;
    admin_name: string;
    admin_password: string;

}

export interface SubTenantResponse {
  id: string;
  domain: string;
  status: string;
  created_at: string;
  subscription_start_date: string;
  subscription_end_date: string;
  days_until_expiration: number;
  // Add other response properties as needed
}



@Injectable({
  providedIn: 'root'
})
export class SubTenantService {

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  /**
   * Create a new sub-tenant
   */
  createSubTenant(request: SubTenantCreateRequest): Observable<SubTenantResponse> {
    return this.http.post<SubTenantResponse>(
      `${this.configService.apiUrl}/sub-tenants/request`,
      request
    );
  }



  /**
   * Get tenants owned by current user (with USER role)
   */
  getUserOwnedTenants(): Observable<SubTenantResponse[]> {
    return this.http.get<SubTenantResponse[]>(
      `${this.configService.apiUrl}/sub-tenants/user/owned`
    );
  }


}