/**
 * Angular CLI proxy configuration with gzip compression support
 * This file provides more flexibility than the JSON version
 */
module.exports = {
  '/api': {
    target: 'http://localhost:8095',
    secure: false,
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/api': '/api/v1'
    },
    headers: {
      'X-Tenant-ID': 'ae0fd2b1-f6ad-48d5-94b5-eacae0a13067',
      'X-Tenant-Domain': 'autovnfb.dev',
      // Enable gzip compression - match Spring Boot header requirement
      'Accept-Encoding': 'gzip',
      'x-real-ip': '************'
    },
    // Enable compression in the proxy
    compress: true,
    
    // Bypass the proxy for certain requests
    bypass: function(req, res, proxyOptions) {
      // You can add logic here to bypass the proxy for certain requests if needed
      if (req.headers.accept && req.headers.accept.indexOf('html') !== -1) {
        console.log('Skipping proxy for browser request.');
        return '/index.html';
      }
    },
    
    // Handle proxy errors
    onError: function(err, req, res) {
      console.error('Proxy error:', err);
      res.writeHead(500, {
        'Content-Type': 'text/plain'
      });
      res.end('Proxy error: ' + err);
    },
    
    // Log proxy activity and compression info
    onProxyRes: function(proxyRes, req, res) {
      console.log('Response from backend:', proxyRes.statusCode, req.url);
      
      // Log compression info
      const contentEncoding = proxyRes.headers['content-encoding'];
      if (contentEncoding) {
        console.log('Response compressed with:', contentEncoding);
      }
      
      // Log content length for debugging
      const contentLength = proxyRes.headers['content-length'];
      if (contentLength) {
        console.log('Content length:', contentLength);
      }
    },
    
    // Modify request before sending to backend
    onProxyReq: function(proxyReq, req, res) {
      // Set exact header to match Spring Boot requirement
      proxyReq.setHeader('Accept-Encoding', 'gzip');
      console.log('Proxying request:', req.method, req.url);
      console.log('Target URL:', proxyReq.path);
      console.log('Headers being sent:', proxyReq.getHeaders());
    }
  },
  
  // Add additional proxy configurations if needed
  // For example, if you have a separate CDN endpoint:
  '/cdn': {
    target: 'https://childautovnfb.click',
    secure: false,
    changeOrigin: true,
    logLevel: 'debug',
    compress: true,
    headers: {
      'Accept-Encoding': 'gzip, deflate, br'
    }
  }
};